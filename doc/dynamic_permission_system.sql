-- 动态权限管理系统数据库扩展脚本
-- 基于现有的 mock_api.sql，添加动态权限管理功能

-- 1. 部门表
CREATE TABLE IF NOT EXISTS sys_department (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '部门ID',
    parent_id BIGINT DEFAULT 0 COMMENT '父部门ID',
    department_name VARCHAR(50) NOT NULL COMMENT '部门名称',
    department_code VARCHAR(30) UNIQUE COMMENT '部门编码',
    leader_id BIGINT COMMENT '部门负责人ID',
    phone VARCHAR(20) COMMENT '联系电话',
    email VARCHAR(50) COMMENT '邮箱',
    sort_order INT DEFAULT 0 COMMENT '显示顺序',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    create_by BIGINT COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by BIGINT COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark TEXT COMMENT '备注'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='部门表';

-- 2. 菜单表 (替换原有的权限表概念)
CREATE TABLE IF NOT EXISTS sys_menu (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '菜单ID',
    parent_id BIGINT DEFAULT 0 COMMENT '父菜单ID',
    menu_name VARCHAR(50) NOT NULL COMMENT '菜单名称',
    menu_code VARCHAR(100) UNIQUE COMMENT '菜单编码',
    menu_type TINYINT NOT NULL COMMENT '菜单类型：1-目录，2-菜单，3-按钮',
    path VARCHAR(200) COMMENT '路由路径',
    component VARCHAR(200) COMMENT '组件路径',
    icon VARCHAR(100) COMMENT '菜单图标',
    sort_order INT DEFAULT 0 COMMENT '显示顺序',
    visible TINYINT DEFAULT 1 COMMENT '是否显示：0-隐藏，1-显示',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    perms VARCHAR(100) COMMENT '权限标识',
    query_params VARCHAR(255) COMMENT '路由参数',
    is_frame TINYINT DEFAULT 0 COMMENT '是否外链：0-否，1-是',
    is_cache TINYINT DEFAULT 1 COMMENT '是否缓存：0-不缓存，1-缓存',
    create_by BIGINT COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by BIGINT COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark TEXT COMMENT '备注'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='菜单权限表';

-- 3. 数据权限规则表
CREATE TABLE IF NOT EXISTS sys_data_permission (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '数据权限ID',
    rule_name VARCHAR(100) NOT NULL COMMENT '规则名称',
    rule_code VARCHAR(100) UNIQUE COMMENT '规则编码',
    resource_type VARCHAR(50) NOT NULL COMMENT '资源类型：table-表，column-列',
    resource_name VARCHAR(100) NOT NULL COMMENT '资源名称',
    permission_type TINYINT NOT NULL COMMENT '权限类型：1-查看，2-编辑，3-删除',
    filter_condition TEXT COMMENT '过滤条件SQL',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    create_by BIGINT COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by BIGINT COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark TEXT COMMENT '备注'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据权限规则表';

-- 4. 角色菜单关联表
CREATE TABLE IF NOT EXISTS sys_role_menu (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    menu_id BIGINT NOT NULL COMMENT '菜单ID',
    create_by BIGINT COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_role_menu (role_id, menu_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色菜单关联表';

-- 5. 角色数据权限关联表
CREATE TABLE IF NOT EXISTS sys_role_data_permission (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    data_permission_id BIGINT NOT NULL COMMENT '数据权限ID',
    create_by BIGINT COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_role_data_permission (role_id, data_permission_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色数据权限关联表';

-- 6. 用户数据权限关联表 (用于覆盖角色权限)
CREATE TABLE IF NOT EXISTS sys_user_data_permission (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    data_permission_id BIGINT NOT NULL COMMENT '数据权限ID',
    permission_action TINYINT NOT NULL COMMENT '权限动作：1-授予，0-撤销',
    create_by BIGINT COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_user_data_permission (user_id, data_permission_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户数据权限关联表';

-- 7. 角色继承关系表
CREATE TABLE IF NOT EXISTS sys_role_hierarchy (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
    parent_role_id BIGINT NOT NULL COMMENT '父角色ID',
    child_role_id BIGINT NOT NULL COMMENT '子角色ID',
    inherit_type TINYINT DEFAULT 1 COMMENT '继承类型：1-完全继承，2-部分继承',
    create_by BIGINT COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_role_hierarchy (parent_role_id, child_role_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色继承关系表';

-- 8. 权限缓存表
CREATE TABLE IF NOT EXISTS sys_permission_cache (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
    cache_key VARCHAR(200) NOT NULL COMMENT '缓存键',
    cache_type TINYINT NOT NULL COMMENT '缓存类型：1-用户菜单，2-用户按钮，3-用户数据权限',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    cache_data JSON COMMENT '缓存数据',
    expire_time DATETIME NOT NULL COMMENT '过期时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_cache_key (cache_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限缓存表';

-- 9. 扩展现有用户表
ALTER TABLE sys_user 
ADD COLUMN department_id BIGINT COMMENT '部门ID',
ADD COLUMN data_scope TINYINT DEFAULT 1 COMMENT '数据权限范围：1-全部，2-本部门及下级，3-本部门，4-仅本人，5-自定义';

-- 10. 扩展现有操作日志表
ALTER TABLE sys_operation_log 
ADD COLUMN permission_type VARCHAR(50) COMMENT '权限类型',
ADD COLUMN target_user_id BIGINT COMMENT '目标用户ID',
ADD COLUMN target_role_id BIGINT COMMENT '目标角色ID',
ADD COLUMN permission_details JSON COMMENT '权限详情',
ADD COLUMN old_value JSON COMMENT '变更前值',
ADD COLUMN new_value JSON COMMENT '变更后值';

-- 11. 创建索引
-- 部门表索引
CREATE INDEX idx_department_parent_id ON sys_department(parent_id);
CREATE INDEX idx_department_code ON sys_department(department_code);
CREATE INDEX idx_department_status ON sys_department(status);

-- 菜单表索引
CREATE INDEX idx_menu_parent_id ON sys_menu(parent_id);
CREATE INDEX idx_menu_code ON sys_menu(menu_code);
CREATE INDEX idx_menu_type ON sys_menu(menu_type);
CREATE INDEX idx_menu_status ON sys_menu(status);
CREATE INDEX idx_menu_perms ON sys_menu(perms);

-- 数据权限表索引
CREATE INDEX idx_data_permission_code ON sys_data_permission(rule_code);
CREATE INDEX idx_data_permission_type ON sys_data_permission(resource_type);
CREATE INDEX idx_data_permission_status ON sys_data_permission(status);

-- 关联表索引
CREATE INDEX idx_role_menu_role_id ON sys_role_menu(role_id);
CREATE INDEX idx_role_menu_menu_id ON sys_role_menu(menu_id);
CREATE INDEX idx_role_data_permission_role_id ON sys_role_data_permission(role_id);
CREATE INDEX idx_user_data_permission_user_id ON sys_user_data_permission(user_id);
CREATE INDEX idx_role_hierarchy_parent ON sys_role_hierarchy(parent_role_id);
CREATE INDEX idx_role_hierarchy_child ON sys_role_hierarchy(child_role_id);

-- 缓存表索引
CREATE INDEX idx_permission_cache_user_id ON sys_permission_cache(user_id);
CREATE INDEX idx_permission_cache_type ON sys_permission_cache(cache_type);
CREATE INDEX idx_permission_cache_expire ON sys_permission_cache(expire_time);

-- 用户表新增字段索引
CREATE INDEX idx_user_department_id ON sys_user(department_id);
CREATE INDEX idx_user_data_scope ON sys_user(data_scope);

-- 12. 初始化基础数据

-- 插入根部门
INSERT INTO sys_department (id, parent_id, department_name, department_code, sort_order, status, create_by, remark) 
VALUES (1, 0, '总公司', 'ROOT', 0, 1, 1, '根部门');

-- 插入示例部门
INSERT INTO sys_department (parent_id, department_name, department_code, sort_order, status, create_by, remark) VALUES
(1, '技术部', 'TECH', 1, 1, 1, '技术开发部门'),
(1, '产品部', 'PRODUCT', 2, 1, 1, '产品管理部门'),
(1, '运营部', 'OPERATION', 3, 1, 1, '运营推广部门'),
(2, '前端组', 'FRONTEND', 1, 1, 1, '前端开发组'),
(2, '后端组', 'BACKEND', 2, 1, 1, '后端开发组');

-- 插入系统菜单
INSERT INTO sys_menu (id, parent_id, menu_name, menu_code, menu_type, path, component, icon, sort_order, visible, status, perms, remark) VALUES
(1, 0, '系统管理', 'system', 1, '/system', NULL, 'system', 1, 1, 1, NULL, '系统管理目录'),
(2, 1, '用户管理', 'user', 2, '/system/user', 'system/user/index', 'user', 1, 1, 1, 'system:user:list', '用户管理菜单'),
(3, 1, '角色管理', 'role', 2, '/system/role', 'system/role/index', 'role', 2, 1, 1, 'system:role:list', '角色管理菜单'),
(4, 1, '菜单管理', 'menu', 2, '/system/menu', 'system/menu/index', 'menu', 3, 1, 1, 'system:menu:list', '菜单管理菜单'),
(5, 1, '部门管理', 'dept', 2, '/system/dept', 'system/dept/index', 'dept', 4, 1, 1, 'system:dept:list', '部门管理菜单'),
(6, 1, '权限管理', 'permission', 2, '/system/permission', 'system/permission/index', 'permission', 5, 1, 1, 'system:permission:list', '权限管理菜单');

-- 插入按钮权限
INSERT INTO sys_menu (parent_id, menu_name, menu_code, menu_type, sort_order, visible, status, perms, remark) VALUES
-- 用户管理按钮
(2, '用户新增', 'user:add', 3, 1, 1, 1, 'system:user:add', '用户新增按钮'),
(2, '用户修改', 'user:edit', 3, 2, 1, 1, 'system:user:edit', '用户修改按钮'),
(2, '用户删除', 'user:remove', 3, 3, 1, 1, 'system:user:remove', '用户删除按钮'),
(2, '用户导出', 'user:export', 3, 4, 1, 1, 'system:user:export', '用户导出按钮'),
(2, '用户导入', 'user:import', 3, 5, 1, 1, 'system:user:import', '用户导入按钮'),
(2, '重置密码', 'user:resetPwd', 3, 6, 1, 1, 'system:user:resetPwd', '重置密码按钮'),
-- 角色管理按钮
(3, '角色新增', 'role:add', 3, 1, 1, 1, 'system:role:add', '角色新增按钮'),
(3, '角色修改', 'role:edit', 3, 2, 1, 1, 'system:role:edit', '角色修改按钮'),
(3, '角色删除', 'role:remove', 3, 3, 1, 1, 'system:role:remove', '角色删除按钮'),
(3, '分配权限', 'role:auth', 3, 4, 1, 1, 'system:role:auth', '分配权限按钮'),
-- 菜单管理按钮
(4, '菜单新增', 'menu:add', 3, 1, 1, 1, 'system:menu:add', '菜单新增按钮'),
(4, '菜单修改', 'menu:edit', 3, 2, 1, 1, 'system:menu:edit', '菜单修改按钮'),
(4, '菜单删除', 'menu:remove', 3, 3, 1, 1, 'system:menu:remove', '菜单删除按钮'),
-- 部门管理按钮
(5, '部门新增', 'dept:add', 3, 1, 1, 1, 'system:dept:add', '部门新增按钮'),
(5, '部门修改', 'dept:edit', 3, 2, 1, 1, 'system:dept:edit', '部门修改按钮'),
(5, '部门删除', 'dept:remove', 3, 3, 1, 1, 'system:dept:remove', '部门删除按钮');

-- 插入数据权限规则
INSERT INTO sys_data_permission (rule_name, rule_code, resource_type, resource_name, permission_type, filter_condition, status, create_by, remark) VALUES
('全部数据权限', 'DATA_SCOPE_ALL', 'table', '*', 1, '', 1, 1, '查看全部数据'),
('本部门数据权限', 'DATA_SCOPE_DEPT', 'table', '*', 1, 'department_id = #{currentUser.departmentId}', 1, 1, '查看本部门数据'),
('本部门及下级数据权限', 'DATA_SCOPE_DEPT_AND_CHILD', 'table', '*', 1, 'department_id IN (#{deptIds})', 1, 1, '查看本部门及下级数据'),
('仅本人数据权限', 'DATA_SCOPE_SELF', 'table', '*', 1, 'create_by = #{currentUser.userId}', 1, 1, '仅查看本人数据'),
('用户敏感信息权限', 'USER_SENSITIVE_INFO', 'column', 'sys_user', 1, 'phone,email,id_card', 1, 1, '用户敏感信息查看权限');

-- 为超级管理员角色分配所有菜单权限
INSERT INTO sys_role_menu (role_id, menu_id, create_by)
SELECT 1, id, 1 FROM sys_menu WHERE status = 1;

-- 为超级管理员角色分配全部数据权限
INSERT INTO sys_role_data_permission (role_id, data_permission_id, create_by)
SELECT 1, id, 1 FROM sys_data_permission WHERE rule_code = 'DATA_SCOPE_ALL';

-- 13. 创建外键约束
ALTER TABLE sys_user ADD CONSTRAINT fk_user_department FOREIGN KEY (department_id) REFERENCES sys_department(id);
ALTER TABLE sys_department ADD CONSTRAINT fk_dept_leader FOREIGN KEY (leader_id) REFERENCES sys_user(id);
ALTER TABLE sys_role_menu ADD CONSTRAINT fk_role_menu_role FOREIGN KEY (role_id) REFERENCES sys_role(id) ON DELETE CASCADE;
ALTER TABLE sys_role_menu ADD CONSTRAINT fk_role_menu_menu FOREIGN KEY (menu_id) REFERENCES sys_menu(id) ON DELETE CASCADE;
ALTER TABLE sys_role_data_permission ADD CONSTRAINT fk_role_data_permission_role FOREIGN KEY (role_id) REFERENCES sys_role(id) ON DELETE CASCADE;
ALTER TABLE sys_role_data_permission ADD CONSTRAINT fk_role_data_permission_data FOREIGN KEY (data_permission_id) REFERENCES sys_data_permission(id) ON DELETE CASCADE;
ALTER TABLE sys_user_data_permission ADD CONSTRAINT fk_user_data_permission_user FOREIGN KEY (user_id) REFERENCES sys_user(id) ON DELETE CASCADE;
ALTER TABLE sys_user_data_permission ADD CONSTRAINT fk_user_data_permission_data FOREIGN KEY (data_permission_id) REFERENCES sys_data_permission(id) ON DELETE CASCADE;
ALTER TABLE sys_role_hierarchy ADD CONSTRAINT fk_role_hierarchy_parent FOREIGN KEY (parent_role_id) REFERENCES sys_role(id) ON DELETE CASCADE;
ALTER TABLE sys_role_hierarchy ADD CONSTRAINT fk_role_hierarchy_child FOREIGN KEY (child_role_id) REFERENCES sys_role(id) ON DELETE CASCADE;

-- 14. 创建视图
-- 用户权限视图
CREATE VIEW v_user_permissions AS
SELECT 
    u.id as user_id,
    u.username,
    u.nickname,
    d.department_name,
    r.role_name,
    m.menu_name,
    m.perms,
    m.menu_type
FROM sys_user u
LEFT JOIN sys_department d ON u.department_id = d.id
LEFT JOIN sys_user_role ur ON u.id = ur.user_id
LEFT JOIN sys_role r ON ur.role_id = r.id
LEFT JOIN sys_role_menu rm ON r.id = rm.role_id
LEFT JOIN sys_menu m ON rm.menu_id = m.id
WHERE u.status = 1 AND r.status = 1 AND m.status = 1;

-- 部门层级视图
CREATE VIEW v_department_tree AS
WITH RECURSIVE dept_tree AS (
    SELECT id, parent_id, department_name, department_code, 0 as level, CAST(id AS CHAR(1000)) as path
    FROM sys_department 
    WHERE parent_id = 0 AND status = 1
    
    UNION ALL
    
    SELECT d.id, d.parent_id, d.department_name, d.department_code, dt.level + 1, CONCAT(dt.path, ',', d.id)
    FROM sys_department d
    INNER JOIN dept_tree dt ON d.parent_id = dt.id
    WHERE d.status = 1
)
SELECT * FROM dept_tree;

-- 15. 创建存储过程
DELIMITER //

-- 获取用户所有权限的存储过程
CREATE PROCEDURE GetUserPermissions(IN userId BIGINT)
BEGIN
    -- 获取用户菜单权限
    SELECT DISTINCT m.*
    FROM sys_menu m
    INNER JOIN sys_role_menu rm ON m.id = rm.menu_id
    INNER JOIN sys_user_role ur ON rm.role_id = ur.role_id
    WHERE ur.user_id = userId AND m.status = 1
    ORDER BY m.sort_order;
END //

-- 获取部门及其所有子部门ID的存储过程
CREATE PROCEDURE GetDepartmentChildren(IN deptId BIGINT)
BEGIN
    WITH RECURSIVE dept_children AS (
        SELECT id FROM sys_department WHERE id = deptId AND status = 1
        UNION ALL
        SELECT d.id FROM sys_department d
        INNER JOIN dept_children dc ON d.parent_id = dc.id
        WHERE d.status = 1
    )
    SELECT id FROM dept_children;
END //

DELIMITER ;

-- 16. 创建触发器
-- 权限缓存清理触发器
DELIMITER //

CREATE TRIGGER tr_clear_permission_cache_on_role_menu_change
AFTER INSERT ON sys_role_menu
FOR EACH ROW
BEGIN
    DELETE FROM sys_permission_cache 
    WHERE user_id IN (
        SELECT user_id FROM sys_user_role WHERE role_id = NEW.role_id
    );
END //

CREATE TRIGGER tr_clear_permission_cache_on_role_menu_delete
AFTER DELETE ON sys_role_menu
FOR EACH ROW
BEGIN
    DELETE FROM sys_permission_cache 
    WHERE user_id IN (
        SELECT user_id FROM sys_user_role WHERE role_id = OLD.role_id
    );
END //

CREATE TRIGGER tr_clear_permission_cache_on_user_role_change
AFTER INSERT ON sys_user_role
FOR EACH ROW
BEGIN
    DELETE FROM sys_permission_cache WHERE user_id = NEW.user_id;
END //

CREATE TRIGGER tr_clear_permission_cache_on_user_role_delete
AFTER DELETE ON sys_user_role
FOR EACH ROW
BEGIN
    DELETE FROM sys_permission_cache WHERE user_id = OLD.user_id;
END //

DELIMITER ;

-- 17. 创建定时清理过期缓存的事件
SET GLOBAL event_scheduler = ON;

CREATE EVENT IF NOT EXISTS ev_clean_expired_permission_cache
ON SCHEDULE EVERY 1 HOUR
DO
  DELETE FROM sys_permission_cache WHERE expire_time < NOW();

-- 脚本执行完成
SELECT '动态权限管理系统数据库扩展脚本执行完成！' as message;