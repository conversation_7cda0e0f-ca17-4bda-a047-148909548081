# Mock服务系统数据库设计文档

## 1. 数据库概述

### 1.1 数据库选型
- **数据库类型**: MySQL 8.0
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_general_ci
- **存储引擎**: InnoDB
- **数据库名**: mock_service

### 1.2 设计原则
- 遵循三范式设计原则
- 合理使用索引提升查询性能
- 使用软删除保留数据历史
- 统一使用时间戳记录时间
- 敏感数据加密存储

### 1.3 命名规范
- 表名使用小写字母和下划线
- 字段名使用小写字母和下划线
- 主键统一命名为id
- 外键命名格式：表名_id
- 索引命名：idx_字段名

## 2. 用户管理模块

### 2.1 用户表（sys_user）

| 字段名 | 类型 | 长度 | 主键 | 非空 | 默认值 | 说明 |
|--------|------|------|------|------|--------|------|
| id | bigint | 20 | 是 | 是 | | 用户ID |
| username | varchar | 50 | | 是 | | 用户名 |
| password | varchar | 100 | | 是 | | 密码(加密) |
| nickname | varchar | 50 | | | NULL | 昵称 |
| email | varchar | 100 | | | NULL | 邮箱 |
| mobile | varchar | 20 | | | NULL | 手机号 |
| avatar | varchar | 255 | | | NULL | 头像URL |
| status | tinyint | 1 | | 是 | 1 | 状态:0-禁用,1-正常 |
| last_login_time | datetime | | | | NULL | 最后登录时间 |
| last_login_ip | varchar | 50 | | | NULL | 最后登录IP |
| deleted | tinyint | 1 | | 是 | 0 | 删除标记 |
| create_time | datetime | | | 是 | | 创建时间 |
| update_time | datetime | | | | NULL | 更新时间 |

**索引**:
- 唯一索引: uk_username (username)
- 唯一索引: uk_email (email)
- 唯一索引: uk_mobile (mobile)
- 普通索引: idx_status (status)

### 2.2 角色表（sys_role）

| 字段名 | 类型 | 长度 | 主键 | 非空 | 默认值 | 说明 |
|--------|------|------|------|------|--------|------|
| id | bigint | 20 | 是 | 是 | | 角色ID |
| role_name | varchar | 50 | | 是 | | 角色名称 |
| role_code | varchar | 50 | | 是 | | 角色编码 |
| description | varchar | 200 | | | NULL | 描述 |
| status | tinyint | 1 | | 是 | 1 | 状态 |
| deleted | tinyint | 1 | | 是 | 0 | 删除标记 |
| create_time | datetime | | | 是 | | 创建时间 |
| update_time | datetime | | | | NULL | 更新时间 |

**索引**:
- 唯一索引: uk_role_code (role_code)

### 2.3 权限表（sys_permission）

| 字段名 | 类型 | 长度 | 主键 | 非空 | 默认值 | 说明 |
|--------|------|------|------|------|--------|------|
| id | bigint | 20 | 是 | 是 | | 权限ID |
| parent_id | bigint | 20 | | | 0 | 父权限ID |
| permission_name | varchar | 50 | | 是 | | 权限名称 |
| permission_code | varchar | 100 | | 是 | | 权限编码 |
| permission_type | tinyint | 1 | | 是 | | 类型:1-菜单,2-按钮,3-接口 |
| path | varchar | 200 | | | NULL | 路径 |
| icon | varchar | 50 | | | NULL | 图标 |
| sort | int | 11 | | | 0 | 排序 |
| status | tinyint | 1 | | 是 | 1 | 状态 |
| deleted | tinyint | 1 | | 是 | 0 | 删除标记 |
| create_time | datetime | | | 是 | | 创建时间 |

**索引**:
- 唯一索引: uk_permission_code (permission_code)
- 普通索引: idx_parent_id (parent_id)

### 2.4 用户角色关联表（sys_user_role）

| 字段名 | 类型 | 长度 | 主键 | 非空 | 默认值 | 说明 |
|--------|------|------|------|------|--------|------|
| id | bigint | 20 | 是 | 是 | | ID |
| user_id | bigint | 20 | | 是 | | 用户ID |
| role_id | bigint | 20 | | 是 | | 角色ID |
| create_time | datetime | | | 是 | | 创建时间 |

**索引**:
- 唯一索引: uk_user_role (user_id, role_id)

### 2.5 角色权限关联表（sys_role_permission）

| 字段名 | 类型 | 长度 | 主键 | 非空 | 默认值 | 说明 |
|--------|------|------|------|------|--------|------|
| id | bigint | 20 | 是 | 是 | | ID |
| role_id | bigint | 20 | | 是 | | 角色ID |
| permission_id | bigint | 20 | | 是 | | 权限ID |
| create_time | datetime | | | 是 | | 创建时间 |

**索引**:
- 唯一索引: uk_role_permission (role_id, permission_id)

## 3. Mock业务模块

### 3.1 Mock项目表（mock_project）

| 字段名 | 类型 | 长度 | 主键 | 非空 | 默认值 | 说明 |
|--------|------|------|------|------|--------|------|
| id | bigint | 20 | 是 | 是 | | 项目ID |
| project_name | varchar | 100 | | 是 | | 项目名称 |
| project_code | varchar | 50 | | 是 | | 项目编码 |
| description | varchar | 500 | | | NULL | 项目描述 |
| project_type | varchar | 20 | | | NULL | 项目类型 |
| base_url | varchar | 200 | | | NULL | 基础URL |
| owner_id | bigint | 20 | | 是 | | 负责人ID |
| status | tinyint | 1 | | 是 | 1 | 状态 |
| is_public | tinyint | 1 | | | 0 | 是否公开 |
| deleted | tinyint | 1 | | 是 | 0 | 删除标记 |
| create_time | datetime | | | 是 | | 创建时间 |
| update_time | datetime | | | | NULL | 更新时间 |
| create_by | bigint | 20 | | | NULL | 创建人 |
| update_by | bigint | 20 | | | NULL | 更新人 |

**索引**:
- 唯一索引: uk_project_code (project_code)
- 普通索引: idx_owner_id (owner_id)
- 普通索引: idx_status (status)

### 3.2 API接口表（mock_api）

| 字段名 | 类型 | 长度 | 主键 | 非空 | 默认值 | 说明 |
|--------|------|------|------|------|--------|------|
| id | bigint | 20 | 是 | 是 | | 接口ID |
| project_id | bigint | 20 | | 是 | | 项目ID |
| group_id | bigint | 20 | | | NULL | 分组ID |
| api_name | varchar | 100 | | 是 | | 接口名称 |
| api_path | varchar | 500 | | 是 | | 接口路径 |
| api_method | varchar | 10 | | 是 | | 请求方法 |
| api_version | varchar | 20 | | | v1 | 接口版本 |
| description | text | | | | NULL | 接口描述 |
| request_headers | json | | | | NULL | 请求头配置 |
| request_params | json | | | | NULL | 请求参数配置 |
| request_body | json | | | | NULL | 请求体配置 |
| response_headers | json | | | | NULL | 响应头配置 |
| response_body | json | | | | NULL | 响应体配置 |
| response_status | int | 11 | | | 200 | 响应状态码 |
| response_delay | int | 11 | | | 0 | 响应延迟(ms) |
| mock_type | varchar | 20 | | | static | Mock类型 |
| status | tinyint | 1 | | 是 | 1 | 状态 |
| deleted | tinyint | 1 | | 是 | 0 | 删除标记 |
| create_time | datetime | | | 是 | | 创建时间 |
| update_time | datetime | | | | NULL | 更新时间 |
| create_by | bigint | 20 | | | NULL | 创建人 |
| update_by | bigint | 20 | | | NULL | 更新人 |

**索引**:
- 唯一索引: uk_project_path_method (project_id, api_path, api_method)
- 普通索引: idx_project_id (project_id)
- 普通索引: idx_group_id (group_id)
- 普通索引: idx_status (status)

### 3.3 Mock规则表（mock_rule）

| 字段名 | 类型 | 长度 | 主键 | 非空 | 默认值 | 说明 |
|--------|------|------|------|------|--------|------|
| id | bigint | 20 | 是 | 是 | | 规则ID |
| api_id | bigint | 20 | | 是 | | 接口ID |
| rule_name | varchar | 100 | | 是 | | 规则名称 |
| rule_type | varchar | 20 | | 是 | | 规则类型 |
| condition_type | varchar | 20 | | | NULL | 条件类型 |
| condition_key | varchar | 100 | | | NULL | 条件键 |
| condition_operator | varchar | 20 | | | NULL | 条件操作符 |
| condition_value | varchar | 500 | | | NULL | 条件值 |
| response_data | json | | | 是 | | 响应数据 |
| response_status | int | 11 | | | 200 | 响应状态码 |
| priority | int | 11 | | | 0 | 优先级 |
| enabled | tinyint | 1 | | | 1 | 是否启用 |
| deleted | tinyint | 1 | | 是 | 0 | 删除标记 |
| create_time | datetime | | | 是 | | 创建时间 |
| update_time | datetime | | | | NULL | 更新时间 |

**索引**:
- 普通索引: idx_api_id (api_id)
- 普通索引: idx_enabled (enabled)
- 普通索引: idx_priority (priority)

### 3.4 接口分组表（mock_api_group）

| 字段名 | 类型 | 长度 | 主键 | 非空 | 默认值 | 说明 |
|--------|------|------|------|------|--------|------|
| id | bigint | 20 | 是 | 是 | | 分组ID |
| project_id | bigint | 20 | | 是 | | 项目ID |
| parent_id | bigint | 20 | | | 0 | 父分组ID |
| group_name | varchar | 100 | | 是 | | 分组名称 |
| description | varchar | 500 | | | NULL | 分组描述 |
| sort | int | 11 | | | 0 | 排序 |
| deleted | tinyint | 1 | | 是 | 0 | 删除标记 |
| create_time | datetime | | | 是 | | 创建时间 |
| update_time | datetime | | | | NULL | 更新时间 |

**索引**:
- 普通索引: idx_project_id (project_id)
- 普通索引: idx_parent_id (parent_id)

## 4. 日志模块

### 4.1 请求日志表（mock_request_log）

| 字段名 | 类型 | 长度 | 主键 | 非空 | 默认值 | 说明 |
|--------|------|------|------|------|--------|------|
| id | bigint | 20 | 是 | 是 | | 日志ID |
| project_id | bigint | 20 | | 是 | | 项目ID |
| api_id | bigint | 20 | | | NULL | 接口ID |
| request_id | varchar | 50 | | 是 | | 请求ID |
| request_method | varchar | 10 | | 是 | | 请求方法 |
| request_path | varchar | 500 | | 是 | | 请求路径 |
| request_headers | json | | | | NULL | 请求头 |
| request_params | text | | | | NULL | 请求参数 |
| request_body | text | | | | NULL | 请求体 |
| response_status | int | 11 | | | NULL | 响应状态码 |
| response_headers | json | | | | NULL | 响应头 |
| response_body | text | | | | NULL | 响应体 |
| response_time | bigint | 20 | | | NULL | 响应时间(ms) |
| client_ip | varchar | 50 | | | NULL | 客户端IP |
| user_agent | varchar | 500 | | | NULL | User-Agent |
| error_message | text | | | | NULL | 错误信息 |
| create_time | datetime | | | 是 | | 创建时间 |

**索引**:
- 普通索引: idx_project_id (project_id)
- 普通索引: idx_api_id (api_id)
- 普通索引: idx_request_id (request_id)
- 普通索引: idx_create_time (create_time)

### 4.2 操作日志表（sys_operation_log）

| 字段名 | 类型 | 长度 | 主键 | 非空 | 默认值 | 说明 |
|--------|------|------|------|------|--------|------|
| id | bigint | 20 | 是 | 是 | | 日志ID |
| user_id | bigint | 20 | | | NULL | 用户ID |
| username | varchar | 50 | | | NULL | 用户名 |
| operation_type | varchar | 50 | | 是 | | 操作类型 |
| operation_desc | varchar | 200 | | | NULL | 操作描述 |
| request_method | varchar | 10 | | | NULL | 请求方法 |
| request_url | varchar | 500 | | | NULL | 请求URL |
| request_params | text | | | | NULL | 请求参数 |
| response_data | text | | | | NULL | 响应数据 |
| error_message | text | | | | NULL | 错误信息 |
| operation_time | bigint | 20 | | | NULL | 操作耗时(ms) |
| client_ip | varchar | 50 | | | NULL | 客户端IP |
| status | tinyint | 1 | | | NULL | 操作状态 |
| create_time | datetime | | | 是 | | 创建时间 |

**索引**:
- 普通索引: idx_user_id (user_id)
- 普通索引: idx_operation_type (operation_type)
- 普通索引: idx_create_time (create_time)

## 5. 协作模块

### 5.1 项目成员表（mock_project_member）

| 字段名 | 类型 | 长度 | 主键 | 非空 | 默认值 | 说明 |
|--------|------|------|------|------|--------|------|
| id | bigint | 20 | 是 | 是 | | ID |
| project_id | bigint | 20 | | 是 | | 项目ID |
| user_id | bigint | 20 | | 是 | | 用户ID |
| role_type | varchar | 20 | | 是 | | 角色类型 |
| join_time | datetime | | | 是 | | 加入时间 |
| invite_by | bigint | 20 | | | NULL | 邀请人ID |

**索引**:
- 唯一索引: uk_project_user (project_id, user_id)

### 5.2 数据模板表（mock_data_template）

| 字段名 | 类型 | 长度 | 主键 | 非空 | 默认值 | 说明 |
|--------|------|------|------|------|--------|------|
| id | bigint | 20 | 是 | 是 | | 模板ID |
| project_id | bigint | 20 | | 是 | | 项目ID |
| template_name | varchar | 100 | | 是 | | 模板名称 |
| template_code | varchar | 50 | | 是 | | 模板编码 |
| description | varchar | 500 | | | NULL | 模板描述 |
| template_schema | json | | | 是 | | 模板结构 |
| template_data | json | | | | NULL | 模板数据 |
| version | varchar | 20 | | | v1 | 版本号 |
| status | tinyint | 1 | | | 1 | 状态 |
| deleted | tinyint | 1 | | 是 | 0 | 删除标记 |
| create_time | datetime | | | 是 | | 创建时间 |
| update_time | datetime | | | | NULL | 更新时间 |
| create_by | bigint | 20 | | | NULL | 创建人 |
| update_by | bigint | 20 | | | NULL | 更新人 |

**索引**:
- 唯一索引: uk_project_code (project_id, template_code)
- 普通索引: idx_project_id (project_id)
- 普通索引: idx_status (status)

## 6. 数据库初始化脚本

### 6.1 创建数据库
```sql
CREATE DATABASE IF NOT EXISTS mock_service_db 
DEFAULT CHARACTER SET utf8mb4 
DEFAULT COLLATE utf8mb4_general_ci;

USE mock_service_db;
```

### 6.2 初始化数据

#### 初始化角色
```sql
INSERT INTO sys_role (role_name, role_code, description) VALUES
('超级管理员', 'ROLE_ADMIN', '系统所有权限'),
('项目管理员', 'ROLE_PROJECT_ADMIN', '项目管理权限'),
('开发人员', 'ROLE_DEVELOPER', '接口开发权限'),
('测试人员', 'ROLE_TESTER', '接口测试权限'),
('访客', 'ROLE_GUEST', '只读权限');
```

#### 初始化权限
```sql
INSERT INTO sys_permission (permission_name, permission_code, permission_type, path) VALUES
('用户管理', 'user:manage', 1, '/users'),
('项目管理', 'project:manage', 1, '/projects'),
('接口管理', 'api:manage', 1, '/apis'),
('日志查看', 'log:view', 1, '/logs'),
('系统设置', 'system:config', 1, '/system');
```

#### 初始化管理员账号
```sql
INSERT INTO sys_user (username, password, nickname, email, status) VALUES
('admin', '$2a$10$encrypted_password', '系统管理员', '<EMAIL>', 1);
```

## 7. 性能优化建议

### 7.1 索引优化
- 为高频查询字段建立索引
- 避免过多索引影响写入性能
- 定期分析索引使用情况
- 使用覆盖索引减少回表

### 7.2 查询优化
- 避免SELECT *
- 使用LIMIT限制返回数据量
- 合理使用JOIN避免笛卡尔积
- 使用EXPLAIN分析查询计划

### 7.3 表结构优化
- 大表考虑分区
- 日志表定期归档
- 使用合适的字段类型
- 避免过度范式化

### 7.4 数据库配置优化
```ini
# my.cnf配置建议
[mysqld]
innodb_buffer_pool_size = 4G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
max_connections = 500
query_cache_size = 128M
query_cache_type = 1
```

## 8. 备份恢复策略

### 8.1 备份策略
- 全量备份：每周日凌晨2点
- 增量备份：每天凌晨2点
- 备份保留：30天
- 异地备份：实时同步

### 8.2 备份脚本
```bash
#!/bin/bash
# 全量备份
mysqldump -h localhost -u root -p$PASSWORD \
  --all-databases \
  --single-transaction \
  --quick \
  --lock-tables=false \
  > /backup/mysql/full_$(date +%Y%m%d).sql

# 压缩备份文件
gzip /backup/mysql/full_$(date +%Y%m%d).sql
```

### 8.3 恢复流程
1. 停止应用服务
2. 恢复数据库备份
3. 验证数据完整性
4. 重启应用服务
5. 功能验证测试

## 9. 监控指标

### 9.1 性能监控
- QPS（每秒查询数）
- TPS（每秒事务数）
- 响应时间
- 连接数
- 缓存命中率

### 9.2 资源监控
- CPU使用率
- 内存使用率
- 磁盘IO
- 网络流量
- 表空间使用率

### 9.3 告警阈值
- 连接数 > 80%
- 响应时间 > 1秒
- 错误率 > 1%
- 磁盘使用率 > 85%
- 主从延迟 > 5秒

---

文档版本：V1.0  
编写日期：2024-01-01  
编写人：数据库架构师  
审核人：技术负责人