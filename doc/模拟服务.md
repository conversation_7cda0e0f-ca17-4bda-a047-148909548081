# 配置文件
```python
server:
  port: 10088
  servlet:
    context-path: /api

spring:
  datasource:
    # 数据库连接信息
    url: ********************************************************************************************************************************************************************************************
    username: TFonQJhoDmQubdd.root
    password: LH6PWmtn2Etgp7qI
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      # 连接池名称
      pool-name: MockServerHikariCP
      # 最小空闲连接数
      minimum-idle: 5
      # 最大连接池大小
      maximum-pool-size: 20
      # 连接超时时间(毫秒)
      connection-timeout: 30000
      # 空闲连接存活最大时间，默认600000（10分钟）
      idle-timeout: 600000
      # 连接最大存活时间，0表示永久存活，默认1800000（30分钟）
      max-lifetime: 1800000
      # 连接测试查询
      connection-test-query: SELECT 1

# MyBatis-Plus 配置
mybatis-plus:
  configuration:
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
    # 日志实现
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      # 主键策略
      id-type: auto
      # 逻辑删除字段
      logic-delete-field: deleted
      # 逻辑删除值
      logic-delete-value: 1
      # 逻辑未删除值
      logic-not-delete-value: 0
  # mapper文件位置
  mapper-locations: classpath*:/mapper/**/*.xml

# 日志配置
logging:
  level:
    cn.bughub: debug
    com.baomidou.mybatisplus: debug
```

# APIMock服务系统
## 1. 接口概述
### 1.1 接口规范
+ **协议**: HTTPS
+ **域名**: [https://api.mockservice.com](https://api.mockservice.com)
+ **版本**: v1
+ **数据格式**: JSON
+ **字符编码**: UTF-8
+ **认证方式**: JWT Bearer Token

### 1.2 公共请求头
| 参数名 | 类型 | 必填 | 说明 |
| --- | --- | --- | --- |
| Authorization | String | 是 | Bearer {token} |
| Content-Type | String | 是 | application/json |
| X-Request-Id | String | 否 | 请求唯一标识 |


### 1.3 统一响应格式
#### 成功响应
```json
{
    "code": 200,
    "message": "success",
    "timestamp": 1640995200000,
    "data": {}
}
```

#### 错误响应
```json
{
    "code": 400,
    "message": "参数错误",
    "errors": []
}
```

## 2. 认证接口
### 2.1 用户注册
+ **URL**: `/api/v1/auth/register`
+ **Method**: `POST`
+ **请求示例**:

```json
{
    "username": "testuser",
    "password": "Test@123456",
    "email": "<EMAIL>"
}
```

### 2.2 用户登录
+ **URL**: `/api/v1/auth/login`
+ **Method**: `POST`
+ **请求示例**:

```json
{
    "account": "testuser",
    "password": "Test@123456"
}
```

### 2.3 刷新Token
+ **URL**: `/api/v1/auth/refresh`
+ **Method**: `POST`

## 3. 项目管理接口
### 3.1 获取项目列表
+ **URL**: `/api/v1/projects`
+ **Method**: `GET`
+ **参数**: pageNum, pageSize, keyword

### 3.2 创建项目
+ **URL**: `/api/v1/projects`
+ **Method**: `POST`

### 3.3 更新项目
+ **URL**: `/api/v1/projects/{projectId}`
+ **Method**: `PUT`

### 3.4 删除项目
+ **URL**: `/api/v1/projects/{projectId}`
+ **Method**: `DELETE`

## 4. Mock接口管理
### 4.1 获取接口列表
+ **URL**: `/api/v1/projects/{projectId}/apis`
+ **Method**: `GET`

### 4.2 创建接口
+ **URL**: `/api/v1/projects/{projectId}/apis`
+ **Method**: `POST`

### 4.3 更新接口
+ **URL**: `/api/v1/projects/{projectId}/apis/{apiId}`
+ **Method**: `PUT`

### 4.4 删除接口
+ **URL**: `/api/v1/projects/{projectId}/apis/{apiId}`
+ **Method**: `DELETE`

## 5. Mock数据调用
### 5.1 调用Mock接口
+ **URL**: `/mock/{projectCode}/{apiPath}`
+ **Method**: `{配置的请求方法}`
+ **说明**: 根据配置返回Mock数据

---

文档版本：V1.0  
更新日期：2024-01-01

# Mock服务系统需求文档
## 1. 项目概述
### 1.1 项目背景
在现代软件开发过程中，前后端分离架构已成为主流。开发过程中经常面临以下问题：

+ 前端开发依赖后端接口，但后端接口尚未完成
+ 测试环境不稳定，影响前端开发和测试效率
+ 第三方接口调用受限，无法进行充分测试
+ 需要模拟各种异常场景和边界条件

Mock服务系统旨在解决上述问题，提供一个功能完善、易于使用的API模拟平台，支持团队协作，提高开发效率。

### 1.2 项目目标
+ 提供企业级Mock服务管理平台
+ 支持多种Mock数据生成策略
+ 实现完善的用户认证和权限管理
+ 支持团队协作和项目管理
+ 提供可视化的接口配置和管理界面
+ 支持请求日志记录和分析

### 1.3 系统定位
本系统定位为企业级Mock服务管理平台，主要服务于：

+ 前端开发人员：快速创建Mock接口，不依赖后端开发进度
+ 后端开发人员：定义接口规范，生成Mock数据
+ 测试人员：模拟各种测试场景，进行接口测试
+ 项目管理人员：管理团队成员，监控项目进度

## 2. 功能需求
### 2.1 用户管理模块
#### 2.1.1 用户注册
+ 支持邮箱注册
+ 支持手机号注册
+ 邮箱/手机验证码验证
+ 密码强度校验（至少8位，包含大小写字母、数字和特殊字符）
+ 用户协议确认

#### 2.1.2 用户登录
+ **账号密码登录**
    - 支持邮箱/手机号/用户名登录
    - 密码错误次数限制（5次后锁定15分钟）
    - 记住登录状态（7天/30天可选）
+ **手机验证码登录**
    - 短信验证码发送（60秒重发限制）
    - 验证码有效期5分钟
    - 每日发送次数限制（同一手机号最多10次）
+ **第三方OAuth2.0登录**
    - GitHub账号登录
    - GitLab账号登录
    - 企业微信登录（可选）
    - 钉钉登录（可选）

#### 2.1.3 用户信息管理
+ 个人资料编辑（昵称、头像、邮箱、手机号）
+ 密码修改（需验证原密码）
+ 密码重置（通过邮箱/手机验证）
+ 账号注销（需二次确认）
+ 登录设备管理
+ 操作日志查看

### 2.2 权限管理模块
#### 2.2.1 角色管理
+ **系统预设角色**
    - 超级管理员：系统所有权限
    - 项目管理员：项目创建、成员管理、接口管理
    - 开发人员：接口创建、编辑、Mock规则配置
    - 测试人员：接口查看、测试、日志查看
    - 访客：只读权限
+ **自定义角色**
    - 角色创建、编辑、删除
    - 权限分配（基于功能模块和操作类型）
    - 角色继承机制

#### 2.2.2 权限控制
+ 基于RBAC（Role-Based Access Control）模型
+ 功能权限：模块访问权限
+ 数据权限：项目、接口级别的数据隔离
+ 操作权限：增删改查等操作权限
+ API接口权限：接口调用权限控制

### 2.3 项目管理模块
#### 2.3.1 项目创建与配置
+ 项目基本信息（名称、描述、图标、标签）
+ 项目类型（Web、App、小程序、开放API）
+ 环境配置（开发、测试、预发布、生产）
+ 全局请求配置
    - BaseURL设置
    - 全局请求头
    - 全局请求参数
    - 超时设置

#### 2.3.2 项目成员管理
+ 成员邀请（邮件邀请、链接邀请）
+ 成员角色分配
+ 成员权限设置
+ 成员移除
+ 访问记录查看

#### 2.3.3 项目分组管理
+ 接口分组创建（支持多级分组）
+ 分组排序
+ 分组权限设置
+ 批量操作支持

### 2.4 Mock接口管理模块
#### 2.4.1 接口创建与编辑
+ **基本信息配置**
    - 接口名称、描述
    - 请求方法（GET、POST、PUT、DELETE、PATCH、HEAD、OPTIONS）
    - 请求路径（支持路径参数）
    - 接口版本管理
    - 接口状态（启用、禁用、废弃）
+ **请求配置**
    - 请求头设置
    - 请求参数（Query、Body、Path）
    - 参数类型定义（String、Number、Boolean、Array、Object）
    - 参数校验规则（必填、格式、范围）
    - 请求体格式（JSON、XML、Form-data、Raw）
+ **响应配置**
    - 响应状态码
    - 响应头设置
    - 响应体配置（支持多种格式）
    - 响应延迟设置（模拟网络延迟）
    - 异常响应配置

#### 2.4.2 Mock规则引擎
+ **静态Mock**
    - 固定返回预设的响应数据
    - 支持JSON、XML、HTML、Text格式
+ **动态Mock**
    - Mock.js语法支持
    - Faker.js集成
    - 自定义函数支持
    - 条件判断逻辑
+ **智能Mock**
    - 基于请求参数的条件响应
    - 基于请求头的条件响应
    - 基于Cookie/Session的状态响应
    - 随机响应（按概率返回不同结果）
+ **代理转发**
    - 配置真实后端地址
    - 请求转发规则
    - 响应拦截和修改
    - 降级策略配置

#### 2.4.3 数据模板管理
+ 数据模型定义
+ 字段类型配置
+ 数据生成规则
+ 模板复用和继承
+ 模板版本管理

### 2.5 Mock数据管理模块
#### 2.5.1 数据生成策略
+ **基础数据类型**
    - 字符串：固定值、随机字符串、正则表达式
    - 数字：固定值、范围随机、递增序列
    - 布尔值：固定值、随机
    - 日期时间：固定值、当前时间、时间范围
    - 枚举：从预设值中选择
+ **复合数据类型**
    - 数组：固定长度、随机长度、循环生成
    - 对象：嵌套结构、引用其他模板
    - 文件：模拟文件上传响应
+ **业务数据类型**
    - 个人信息：姓名、身份证、手机号、邮箱
    - 地址信息：省市区、详细地址、邮编
    - 公司信息：公司名、统一社会信用代码
    - 金融信息：银行卡号、金额格式
    - 网络信息：IP地址、URL、域名

#### 2.5.2 数据关联
+ 接口间数据关联
+ 请求响应数据映射
+ 全局变量和环境变量
+ 数据依赖关系配置

#### 2.5.3 测试数据集
+ 批量数据生成
+ 数据导入导出（JSON、CSV、Excel）
+ 测试数据版本管理
+ 数据快照和恢复

### 2.6 请求日志模块
#### 2.6.1 日志记录
+ 请求信息记录
    - 请求时间
    - 请求方法和路径
    - 请求头
    - 请求参数
    - 请求体
    - 客户端IP
    - User-Agent
+ 响应信息记录
    - 响应状态码
    - 响应头
    - 响应体
    - 响应时间
    - 处理耗时

#### 2.6.2 日志查询
+ 多条件组合查询
+ 时间范围筛选
+ 关键词搜索
+ 高级过滤（状态码、耗时、IP等）
+ 日志导出

#### 2.6.3 日志分析
+ 请求统计（总量、成功率、平均耗时）
+ 接口调用排行
+ 错误分析
+ 性能分析
+ 可视化图表展示

### 2.7 团队协作模块
#### 2.7.1 协作功能
+ 实时协作编辑
+ 变更通知
+ 评论和讨论
+ 任务分配
+ 进度跟踪

#### 2.7.2 版本控制
+ 接口版本管理
+ 变更历史记录
+ 版本对比
+ 版本回滚
+ 分支管理

#### 2.7.3 文档生成
+ 自动生成API文档
+ 支持多种文档格式（Markdown、HTML、PDF）
+ 文档模板自定义
+ 在线文档预览
+ 文档分享和导出

### 2.8 系统管理模块
#### 2.8.1 系统配置
+ 基础配置（系统名称、Logo、主题）
+ 邮件服务配置
+ 短信服务配置
+ 存储配置（本地、OSS）
+ 缓存配置

#### 2.8.2 安全设置
+ 登录安全策略
+ 密码策略
+ IP白名单/黑名单
+ 访问频率限制
+ 敏感操作二次验证

#### 2.8.3 监控告警
+ 系统运行状态监控
+ 资源使用监控
+ 异常告警配置
+ 告警通知方式（邮件、短信、webhook）

## 3. 非功能性需求
### 3.1 性能需求
+ **响应时间**
    - 页面加载时间 < 2秒
    - API接口响应时间 < 200ms（不含Mock延迟）
    - Mock接口响应时间 < 100ms（不含设置的延迟）
+ **并发能力**
    - 支持10000个并发用户
    - 单个Mock接口QPS > 1000
    - 数据库连接池：最小50，最大200
+ **数据容量**
    - 支持100万+接口定义
    - 支持1000万+日志记录
    - 单个项目接口数量 < 10000
    - 单个响应体大小 < 10MB

### 3.2 可用性需求
+ 系统可用性 > 99.9%
+ 支持故障自动恢复
+ 支持热部署和灰度发布
+ 数据备份策略（每日增量、每周全量）
+ 灾难恢复RTO < 4小时，RPO < 1小时

### 3.3 安全需求
+ **数据安全**
    - 敏感数据加密存储（AES-256）
    - 数据传输加密（HTTPS/TLS 1.2+）
    - SQL注入防护
    - XSS攻击防护
    - CSRF防护
+ **访问控制**
    - JWT Token认证
    - Token过期时间管理
    - RefreshToken机制
    - 多设备登录控制
    - API访问频率限制
+ **审计日志**
    - 用户操作日志
    - 系统访问日志
    - 数据变更日志
    - 安全事件日志

### 3.4 兼容性需求
+ **浏览器兼容**
    - Chrome 90+
    - Firefox 88+
    - Safari 14+
    - Edge 90+
+ **移动端适配**
    - 响应式设计
    - 移动端手势支持
    - PWA支持

### 3.5 可维护性需求
+ 模块化设计
+ 代码规范遵循
+ 完善的注释和文档
+ 单元测试覆盖率 > 80%
+ 集成测试覆盖核心流程
+ 日志级别可配置
+ 支持远程调试

### 3.6 可扩展性需求
+ 微服务架构支持
+ 水平扩展能力
+ 插件化机制
+ 第三方集成接口
+ 多语言支持（i18n）

## 4. 系统架构设计
### 4.1 总体架构
采用前后端分离的架构模式，整体分为以下几层：

```plain
┌─────────────────────────────────────────────────────────┐
│                      前端展示层                           │
│         Vite + Vue3 + Vue Router + Pinia + Element Plus  │
└─────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────┐
│                      API网关层                           │
│              Nginx（负载均衡、反向代理、限流）              │
└─────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────┐
│                     应用服务层                           │
│     Spring Boot + Spring Security + JWT + WebSocket      │
├─────────────────────────────────────────────────────────┤
│   认证服务 │ 用户服务 │ Mock服务 │ 日志服务 │ 通知服务    │
└─────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────┐
│                      数据访问层                          │
│              MyBatis-Plus + Druid连接池                  │
└─────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────┐
│                      数据存储层                          │
│     MySQL 8.0（主从）│ Redis 6.0（集群）│ MinIO（文件）   │
└─────────────────────────────────────────────────────────┘
```

### 4.2 前端架构设计
#### 4.2.1 技术选型
+ **构建工具**：Vite 4.x
+ **框架**：Vue 3.3+
+ **路由**：Vue Router 4.x
+ **状态管理**：Pinia 2.x
+ **UI组件库**：Element Plus 2.x
+ **HTTP客户端**：Axios
+ **图表**：ECharts 5.x
+ **代码编辑器**：Monaco Editor
+ **Mock数据**：Mock.js
+ **工具库**：Lodash、Day.js

#### 4.2.2 目录结构
```plain
mock-client/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API接口定义
│   ├── assets/            # 资源文件
│   ├── components/        # 公共组件
│   ├── composables/       # 组合式函数
│   ├── directives/        # 自定义指令
│   ├── layouts/           # 布局组件
│   ├── plugins/           # 插件
│   ├── router/            # 路由配置
│   ├── stores/            # Pinia状态管理
│   ├── styles/            # 全局样式
│   ├── utils/             # 工具函数
│   ├── views/             # 页面组件
│   ├── App.vue            # 根组件
│   └── main.js            # 入口文件
├── .env                   # 环境变量
├── .env.development       # 开发环境变量
├── .env.production        # 生产环境变量
├── vite.config.js         # Vite配置
└── package.json           # 项目配置
```

### 4.3 后端架构设计
#### 4.3.1 技术选型
+ **框架**：Spring Boot 2.7.x
+ **安全**：Spring Security + JWT
+ **ORM**：MyBatis-Plus 3.5.x
+ **数据库**：MySQL 8.0
+ **缓存**：Redis 6.x + Caffeine
+ **连接池**：Druid
+ **API文档**：Knife4j (Swagger)
+ **验证**：Hibernate Validator
+ **工具**：Lombok、MapStruct、Hutool

#### 4.3.2 模块划分
```plain
mock-server/
├── mock-common/           # 公共模块
│   ├── constants/        # 常量定义
│   ├── enums/           # 枚举定义
│   ├── exceptions/      # 异常定义
│   ├── utils/           # 工具类
│   └── dto/             # 数据传输对象
├── mock-core/            # 核心业务模块
│   ├── entity/          # 实体类
│   ├── mapper/          # MyBatis Mapper
│   ├── service/         # 业务逻辑
│   ├── controller/      # 控制器
│   └── config/          # 配置类
├── mock-security/        # 安全模块
│   ├── authentication/ # 认证
│   ├── authorization/   # 授权
│   └── jwt/            # JWT处理
├── mock-engine/          # Mock引擎模块
│   ├── parser/         # 规则解析
│   ├── generator/      # 数据生成
│   ├── proxy/          # 代理转发
│   └── cache/          # 缓存处理
└── mock-admin/           # 管理模块
    └── SpringBootApplication # 启动类
```

## 5. 数据库设计
### 5.1 用户相关表
#### 5.1.1 用户表（sys_user）
```sql
CREATE TABLE `sys_user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(100) NOT NULL COMMENT '密码（加密）',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `mobile` varchar(20) DEFAULT NULL COMMENT '手机号',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-正常',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `login_count` int DEFAULT '0' COMMENT '登录次数',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '删除标记：0-正常，1-删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`),
  UNIQUE KEY `uk_mobile` (`mobile`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
```

#### 5.1.2 角色表（sys_role）
```sql
CREATE TABLE `sys_role` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(50) NOT NULL COMMENT '角色名称',
  `role_code` varchar(50) NOT NULL COMMENT '角色编码',
  `description` varchar(200) DEFAULT NULL COMMENT '描述',
  `sort` int DEFAULT '0' COMMENT '排序',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-正常',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '删除标记',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_code` (`role_code`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';
```

#### 5.1.3 权限表（sys_permission）
```sql
CREATE TABLE `sys_permission` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `parent_id` bigint DEFAULT '0' COMMENT '父权限ID',
  `permission_name` varchar(50) NOT NULL COMMENT '权限名称',
  `permission_code` varchar(100) NOT NULL COMMENT '权限编码',
  `permission_type` tinyint NOT NULL COMMENT '权限类型：1-菜单，2-按钮，3-接口',
  `path` varchar(200) DEFAULT NULL COMMENT '路径',
  `component` varchar(200) DEFAULT NULL COMMENT '组件路径',
  `icon` varchar(50) DEFAULT NULL COMMENT '图标',
  `sort` int DEFAULT '0' COMMENT '排序',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-正常',
  `visible` tinyint DEFAULT '1' COMMENT '是否可见：0-隐藏，1-显示',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '删除标记',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_permission_code` (`permission_code`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限表';
```

#### 5.1.4 用户角色关联表（sys_user_role）
```sql
CREATE TABLE `sys_user_role` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_role` (`user_id`,`role_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_role_id` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';
```

#### 5.1.5 角色权限关联表（sys_role_permission）
```sql
CREATE TABLE `sys_role_permission` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `permission_id` bigint NOT NULL COMMENT '权限ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_permission` (`role_id`,`permission_id`),
  KEY `idx_role_id` (`role_id`),
  KEY `idx_permission_id` (`permission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限关联表';
```

### 5.2 Mock业务相关表
#### 5.2.1 Mock项目表（mock_project）
```sql
CREATE TABLE `mock_project` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '项目ID',
  `project_name` varchar(100) NOT NULL COMMENT '项目名称',
  `project_code` varchar(50) NOT NULL COMMENT '项目编码',
  `description` varchar(500) DEFAULT NULL COMMENT '项目描述',
  `project_type` varchar(20) DEFAULT NULL COMMENT '项目类型：web/app/miniapp/openapi',
  `base_url` varchar(200) DEFAULT NULL COMMENT '基础URL',
  `icon` varchar(255) DEFAULT NULL COMMENT '项目图标',
  `tags` varchar(200) DEFAULT NULL COMMENT '标签（逗号分隔）',
  `owner_id` bigint NOT NULL COMMENT '项目负责人ID',
  `team_id` bigint DEFAULT NULL COMMENT '团队ID',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-正常',
  `is_public` tinyint DEFAULT '0' COMMENT '是否公开：0-私有，1-公开',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '删除标记',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_project_code` (`project_code`),
  KEY `idx_owner_id` (`owner_id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Mock项目表';
```

#### 5.2.2 API接口表（mock_api）
```sql
CREATE TABLE `mock_api` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '接口ID',
  `project_id` bigint NOT NULL COMMENT '项目ID',
  `group_id` bigint DEFAULT NULL COMMENT '分组ID',
  `api_name` varchar(100) NOT NULL COMMENT '接口名称',
  `api_path` varchar(500) NOT NULL COMMENT '接口路径',
  `api_method` varchar(10) NOT NULL COMMENT '请求方法：GET/POST/PUT/DELETE等',
  `api_version` varchar(20) DEFAULT 'v1' COMMENT '接口版本',
  `description` text COMMENT '接口描述',
  `request_headers` json DEFAULT NULL COMMENT '请求头配置',
  `request_params` json DEFAULT NULL COMMENT '请求参数配置',
  `request_body` json DEFAULT NULL COMMENT '请求体配置',
  `response_headers` json DEFAULT NULL COMMENT '响应头配置',
  `response_body` json DEFAULT NULL COMMENT '响应体配置',
  `response_status` int DEFAULT '200' COMMENT '响应状态码',
  `response_delay` int DEFAULT '0' COMMENT '响应延迟（毫秒）',
  `mock_type` varchar(20) DEFAULT 'static' COMMENT 'Mock类型：static/dynamic/smart/proxy',
  `proxy_url` varchar(500) DEFAULT NULL COMMENT '代理转发URL',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用，2-废弃',
  `sort` int DEFAULT '0' COMMENT '排序',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '删除标记',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_project_path_method` (`project_id`,`api_path`,`api_method`),
  KEY `idx_project_id` (`project_id`),
  KEY `idx_group_id` (`group_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API接口表';
```

#### 5.2.3 Mock规则表（mock_rule）
```sql
CREATE TABLE `mock_rule` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '规则ID',
  `api_id` bigint NOT NULL COMMENT '接口ID',
  `rule_name` varchar(100) NOT NULL COMMENT '规则名称',
  `rule_type` varchar(20) NOT NULL COMMENT '规则类型：condition/random/sequence',
  `condition_type` varchar(20) DEFAULT NULL COMMENT '条件类型：param/header/cookie',
  `condition_key` varchar(100) DEFAULT NULL COMMENT '条件键',
  `condition_operator` varchar(20) DEFAULT NULL COMMENT '条件操作符：eq/ne/gt/lt/contains/regex',
  `condition_value` varchar(500) DEFAULT NULL COMMENT '条件值',
  `response_data` json NOT NULL COMMENT '响应数据',
  `response_status` int DEFAULT '200' COMMENT '响应状态码',
  `priority` int DEFAULT '0' COMMENT '优先级（数值越大优先级越高）',
  `probability` decimal(5,2) DEFAULT NULL COMMENT '概率（用于随机规则）',
  `enabled` tinyint DEFAULT '1' COMMENT '是否启用：0-禁用，1-启用',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '删除标记',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_api_id` (`api_id`),
  KEY `idx_enabled` (`enabled`),
  KEY `idx_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Mock规则表';
```

#### 5.2.4 接口分组表（mock_api_group）
```sql
CREATE TABLE `mock_api_group` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分组ID',
  `project_id` bigint NOT NULL COMMENT '项目ID',
  `parent_id` bigint DEFAULT '0' COMMENT '父分组ID',
  `group_name` varchar(100) NOT NULL COMMENT '分组名称',
  `description` varchar(500) DEFAULT NULL COMMENT '分组描述',
  `sort` int DEFAULT '0' COMMENT '排序',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '删除标记',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_project_id` (`project_id`),
  KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='接口分组表';
```

### 5.3 日志相关表
#### 5.3.1 请求日志表（mock_request_log）
```sql
CREATE TABLE `mock_request_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `project_id` bigint NOT NULL COMMENT '项目ID',
  `api_id` bigint DEFAULT NULL COMMENT '接口ID',
  `request_id` varchar(50) NOT NULL COMMENT '请求ID（UUID）',
  `request_method` varchar(10) NOT NULL COMMENT '请求方法',
  `request_path` varchar(500) NOT NULL COMMENT '请求路径',
  `request_headers` json DEFAULT NULL COMMENT '请求头',
  `request_params` text DEFAULT NULL COMMENT '请求参数',
  `request_body` text DEFAULT NULL COMMENT '请求体',
  `response_status` int DEFAULT NULL COMMENT '响应状态码',
  `response_headers` json DEFAULT NULL COMMENT '响应头',
  `response_body` text DEFAULT NULL COMMENT '响应体',
  `response_time` bigint DEFAULT NULL COMMENT '响应时间（毫秒）',
  `client_ip` varchar(50) DEFAULT NULL COMMENT '客户端IP',
  `user_agent` varchar(500) DEFAULT NULL COMMENT 'User-Agent',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_project_id` (`project_id`),
  KEY `idx_api_id` (`api_id`),
  KEY `idx_request_id` (`request_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_client_ip` (`client_ip`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='请求日志表';
```

#### 5.3.2 操作日志表（sys_operation_log）
```sql
CREATE TABLE `sys_operation_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  `username` varchar(50) DEFAULT NULL COMMENT '用户名',
  `operation_type` varchar(50) NOT NULL COMMENT '操作类型',
  `operation_desc` varchar(200) DEFAULT NULL COMMENT '操作描述',
  `request_method` varchar(10) DEFAULT NULL COMMENT '请求方法',
  `request_url` varchar(500) DEFAULT NULL COMMENT '请求URL',
  `request_params` text DEFAULT NULL COMMENT '请求参数',
  `response_data` text DEFAULT NULL COMMENT '响应数据',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
  `operation_time` bigint DEFAULT NULL COMMENT '操作耗时（毫秒）',
  `client_ip` varchar(50) DEFAULT NULL COMMENT '客户端IP',
  `user_agent` varchar(500) DEFAULT NULL COMMENT 'User-Agent',
  `status` tinyint DEFAULT NULL COMMENT '操作状态：0-失败，1-成功',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';
```

### 5.4 协作相关表
#### 5.4.1 项目成员表（mock_project_member）
```sql
CREATE TABLE `mock_project_member` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `project_id` bigint NOT NULL COMMENT '项目ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role_type` varchar(20) NOT NULL COMMENT '角色类型：owner/admin/developer/tester/guest',
  `join_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
  `invite_by` bigint DEFAULT NULL COMMENT '邀请人ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_project_user` (`project_id`,`user_id`),
  KEY `idx_project_id` (`project_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目成员表';
```

#### 5.4.2 数据模板表（mock_data_template）
```sql
CREATE TABLE `mock_data_template` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `project_id` bigint NOT NULL COMMENT '项目ID',
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `template_code` varchar(50) NOT NULL COMMENT '模板编码',
  `description` varchar(500) DEFAULT NULL COMMENT '模板描述',
  `template_schema` json NOT NULL COMMENT '模板结构定义',
  `template_data` json DEFAULT NULL COMMENT '模板数据',
  `version` varchar(20) DEFAULT 'v1' COMMENT '版本号',
  `status` tinyint DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '删除标记',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_project_code` (`project_id`,`template_code`),
  KEY `idx_project_id` (`project_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据模板表';
```

## 6. 接口设计规范
### 6.1 RESTful API设计原则
+ 使用名词而非动词定义资源
+ 使用HTTP方法表示操作类型
+ 使用合适的HTTP状态码
+ 支持分页、排序、过滤
+ 版本控制（URL路径或请求头）

### 6.2 URL设计规范
```plain
基础路径：/api/v1

用户相关：
POST   /api/v1/auth/register          # 用户注册
POST   /api/v1/auth/login             # 用户登录
POST   /api/v1/auth/logout            # 用户登出
POST   /api/v1/auth/refresh           # 刷新Token
GET    /api/v1/users/profile          # 获取个人信息
PUT    /api/v1/users/profile          # 更新个人信息
PUT    /api/v1/users/password         # 修改密码

项目管理：
GET    /api/v1/projects               # 获取项目列表
POST   /api/v1/projects               # 创建项目
GET    /api/v1/projects/{id}          # 获取项目详情
PUT    /api/v1/projects/{id}          # 更新项目
DELETE /api/v1/projects/{id}          # 删除项目

接口管理：
GET    /api/v1/projects/{projectId}/apis           # 获取接口列表
POST   /api/v1/projects/{projectId}/apis           # 创建接口
GET    /api/v1/projects/{projectId}/apis/{id}      # 获取接口详情
PUT    /api/v1/projects/{projectId}/apis/{id}      # 更新接口
DELETE /api/v1/projects/{projectId}/apis/{id}      # 删除接口

Mock服务：
{method} /mock/{projectCode}/{apiPath}              # Mock接口调用
```

### 6.3 请求响应格式
#### 6.3.1 统一请求格式
```json
{
  "timestamp": "2024-01-01T00:00:00Z",
  "requestId": "uuid",
  "data": {
    // 请求数据
  }
}
```

#### 6.3.2 统一响应格式
```json
{
  "code": 200,
  "message": "success",
  "timestamp": "2024-01-01T00:00:00Z",
  "requestId": "uuid",
  "data": {
    // 响应数据
  }
}
```

#### 6.3.3 分页响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [],
    "total": 100,
    "pageNum": 1,
    "pageSize": 10,
    "pages": 10,
    "hasNext": true,
    "hasPrevious": false
  }
}
```

#### 6.3.4 错误响应格式
```json
{
  "code": 400,
  "message": "参数错误",
  "timestamp": "2024-01-01T00:00:00Z",
  "requestId": "uuid",
  "errors": [
    {
      "field": "username",
      "message": "用户名不能为空"
    }
  ]
}
```

### 6.4 状态码规范
+ 200 OK：请求成功
+ 201 Created：创建成功
+ 204 No Content：删除成功
+ 400 Bad Request：请求参数错误
+ 401 Unauthorized：未认证
+ 403 Forbidden：无权限
+ 404 Not Found：资源不存在
+ 409 Conflict：资源冲突
+ 429 Too Many Requests：请求过于频繁
+ 500 Internal Server Error：服务器内部错误
+ 503 Service Unavailable：服务不可用

## 7. 安全策略
### 7.1 认证机制
+ JWT Token认证
+ Token有效期：2小时
+ Refresh Token有效期：7天
+ 多设备登录管理
+ 单点登录支持（SSO）

### 7.2 授权机制
+ 基于RBAC的权限控制
+ 接口级别权限控制
+ 数据级别权限控制
+ 动态权限加载

### 7.3 数据安全
+ 密码加密存储（BCrypt）
+ 敏感数据脱敏
+ SQL注入防护
+ XSS攻击防护
+ CSRF防护
+ 文件上传安全检查

### 7.4 通信安全
+ HTTPS强制使用
+ API签名验证
+ 请求重放攻击防护
+ 限流和防刷机制

### 7.5 审计要求
+ 全量操作日志记录
+ 敏感操作告警
+ 异常行为检测
+ 日志定期归档

## 8. 部署方案
### 8.1 部署架构
```plain
┌─────────────────────────────────────────────────────────┐
│                      CDN                                 │
│                 (静态资源加速)                             │
└─────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────┐
│                  负载均衡器（LB）                          │
│              Nginx/HAProxy (主备模式)                      │
└─────────────────────────────────────────────────────────┘
                              ↓
┌──────────────┬──────────────┬──────────────┬────────────┐
│   Web服务器1  │   Web服务器2  │   Web服务器3  │    ...     │
│   (Nginx)    │   (Nginx)    │   (Nginx)    │           │
└──────────────┴──────────────┴──────────────┴────────────┘
                              ↓
┌──────────────┬──────────────┬──────────────┬────────────┐
│   应用服务器1  │   应用服务器2  │   应用服务器3  │    ...     │
│ (Spring Boot)│ (Spring Boot)│ (Spring Boot)│           │
└──────────────┴──────────────┴──────────────┴────────────┘
                              ↓
┌─────────────────────────────────────────────────────────┐
│                    中间件层                               │
├──────────────┬──────────────┬──────────────┬────────────┤
│  Redis集群    │  消息队列     │   搜索引擎    │  文件存储   │
│  (缓存)      │  (RabbitMQ)  │(Elasticsearch)│  (MinIO)  │
└──────────────┴──────────────┴──────────────┴────────────┘
                              ↓
┌─────────────────────────────────────────────────────────┐
│                    数据库层                               │
├─────────────────────┬───────────────────────────────────┤
│    MySQL主库         │        MySQL从库                   │
│   (写操作)          │       (读操作)                     │
└─────────────────────┴───────────────────────────────────┘
```

### 8.2 服务器配置
#### 8.2.1 应用服务器
+ CPU：8核
+ 内存：16GB
+ 磁盘：100GB SSD
+ 数量：3台起步，支持水平扩展

#### 8.2.2 数据库服务器
+ CPU：16核
+ 内存：32GB
+ 磁盘：500GB SSD
+ 配置：主从复制，读写分离

#### 8.2.3 缓存服务器
+ Redis集群：3主3从
+ 内存：每节点8GB
+ 持久化：AOF + RDB

### 8.3 部署流程
1. **环境准备**
    - 服务器初始化
    - 安装必要软件
    - 配置网络和防火墙
2. **数据库部署**
    - 安装MySQL
    - 配置主从复制
    - 初始化数据库结构
3. **中间件部署**
    - 部署Redis集群
    - 部署消息队列
    - 部署文件存储
4. **应用部署**
    - 构建应用包
    - 配置环境变量
    - 启动应用服务
5. **前端部署**
    - 构建前端资源
    - 配置CDN
    - 部署到Web服务器
6. **监控配置**
    - 配置日志收集
    - 配置性能监控
    - 配置告警规则

### 8.4 容器化部署（Docker）
#### 8.4.1 Docker镜像
```dockerfile
# 后端Dockerfile
FROM openjdk:11-jre-slim
COPY target/mock-server.jar app.jar
ENTRYPOINT ["java", "-jar", "/app.jar"]

# 前端Dockerfile
FROM nginx:alpine
COPY dist/ /usr/share/nginx/html/
COPY nginx.conf /etc/nginx/nginx.conf
```

#### 8.4.2 Docker Compose配置
```yaml
version: '3.8'
services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: mock_db
    volumes:
      - mysql_data:/var/lib/mysql
    
  redis:
    image: redis:6.2-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    
  backend:
    build: ./mock-server
    depends_on:
      - mysql
      - redis
    environment:
      SPRING_PROFILES_ACTIVE: prod
    ports:
      - "8080:8080"
    
  frontend:
    build: ./mock-client
    ports:
      - "80:80"
    depends_on:
      - backend

volumes:
  mysql_data:
  redis_data:
```

### 8.5 Kubernetes部署
```yaml
# Deployment配置示例
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mock-server
spec:
  replicas: 3
  selector:
    matchLabels:
      app: mock-server
  template:
    metadata:
      labels:
        app: mock-server
    spec:
      containers:
      - name: mock-server
        image: mock-server:latest
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "k8s"
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
```

## 9. 性能指标要求
### 9.1 响应时间要求
| 操作类型 | 目标响应时间 | 最大响应时间 |
| --- | --- | --- |
| 页面加载 | < 1s | < 2s |
| API查询 | < 100ms | < 200ms |
| Mock接口 | < 50ms | < 100ms |
| 数据保存 | < 200ms | < 500ms |
| 文件上传 | < 5s/MB | < 10s/MB |
| 批量操作 | < 3s | < 5s |


### 9.2 并发性能要求
+ 系统总并发用户数：10000+
+ 单接口QPS：1000+
+ Mock服务QPS：5000+
+ WebSocket连接数：5000+

### 9.3 可用性要求
+ 系统可用性：99.9%
+ 年度停机时间：< 8.76小时
+ 平均故障恢复时间：< 30分钟
+ 数据备份频率：每日增量，每周全量

### 9.4 容量要求
+ 用户数量：100万+
+ 项目数量：10万+
+ 接口数量：100万+
+ 日志保留：90天
+ 数据增长率：每月20%

### 9.5 监控指标
+ CPU使用率：< 70%
+ 内存使用率：< 80%
+ 磁盘使用率：< 85%
+ 网络带宽使用率：< 70%
+ 数据库连接池使用率：< 80%
+ 缓存命中率：> 90%

## 10. 项目里程碑
### 10.1 第一阶段（MVP版本）- 2个月
+ 用户注册登录（账号密码）
+ 项目管理基础功能
+ 静态Mock接口
+ 基础的请求日志
+ 简单的权限控制

### 10.2 第二阶段（基础版本）- 2个月
+ 完整的用户认证（多种登录方式）
+ 动态Mock支持
+ Mock规则引擎
+ 团队协作功能
+ API文档生成

### 10.3 第三阶段（进阶版本）- 2个月
+ 智能Mock功能
+ 代理转发功能
+ 数据模板管理
+ 性能优化
+ 监控告警

### 10.4 第四阶段（企业版本）- 3个月
+ 企业级权限管理
+ 多租户支持
+ 插件系统
+ 开放API
+ 商业化功能

## 11. 风险评估与应对
### 11.1 技术风险
| 风险项 | 可能性 | 影响程度 | 应对措施 |
| --- | --- | --- | --- |
| 高并发性能问题 | 中 | 高 | 采用缓存、异步处理、负载均衡 |
| 数据安全风险 | 中 | 高 | 加密存储、访问控制、安全审计 |
| 系统稳定性 | 低 | 高 | 完善监控、自动化测试、灰度发布 |
| 技术选型风险 | 低 | 中 | 充分调研、原型验证、技术储备 |


### 11.2 业务风险
| 风险项 | 可能性 | 影响程度 | 应对措施 |
| --- | --- | --- | --- |
| 需求变更 | 高 | 中 | 敏捷开发、快速迭代、模块化设计 |
| 用户接受度 | 中 | 高 | 用户调研、易用性设计、培训支持 |
| 竞品竞争 | 中 | 中 | 差异化功能、持续创新、优质服务 |


## 12. 总结
Mock服务系统是一个功能完善的企业级API模拟平台，通过提供强大的Mock功能、完善的权限管理、便捷的团队协作等特性，能够有效提升开发效率，降低开发成本。系统采用前后端分离架构，使用主流技术栈，具有良好的扩展性和维护性。

本需求文档详细描述了系统的功能需求、技术架构、数据库设计、接口规范、安全策略、部署方案和性能指标，为项目的开发实施提供了全面的指导。在实际开发过程中，可根据具体情况对需求进行调整和优化，确保系统能够满足实际业务需求。

---

文档版本：V1.0  
编写日期：2024-01-01  
编写人：系统架构师  
审核人：项目经理

