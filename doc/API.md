# APIMock服务系统
## 1. 接口概述
### 1.1 接口规范
+ **协议**: HTTPS
+ **域名**: [https://api.mockservice.com](https://api.mockservice.com)
+ **版本**: v1
+ **数据格式**: JSON
+ **字符编码**: UTF-8
+ **认证方式**: JWT Bearer Token

### 1.2 公共请求头
| 参数名 | 类型 | 必填 | 说明 |
| --- | --- | --- | --- |
| Authorization | String | 是 | Bearer {token} |
| Content-Type | String | 是 | application/json |
| X-Request-Id | String | 否 | 请求唯一标识 |


### 1.3 统一响应格式
#### 成功响应
```json
{
    "code": 200,
    "message": "success",
    "timestamp": *************,
    "data": {}
}
```

#### 错误响应
```json
{
    "code": 400,
    "message": "参数错误",
    "errors": []
}
```

## 2. 认证接口
### 2.1 用户注册
+ **URL**: `/api/v1/auth/register`
+ **Method**: `POST`
+ **请求示例**:

```json
{
    "username": "testuser",
    "password": "Test@123456",
    "email": "<EMAIL>"
}
```

### 2.2 用户登录
+ **URL**: `/api/v1/auth/login`
+ **Method**: `POST`
+ **请求示例**:

```json
{
    "account": "testuser",
    "password": "Test@123456"
}
```

### 2.3 刷新Token
+ **URL**: `/api/v1/auth/refresh`
+ **Method**: `POST`
+
## 3. 项目管理接口
### 3.1 获取项目列表
+ **URL**: `/api/v1/projects`
+ **Method**: `GET`
+ **参数**: pageNum, pageSize, keyword

### 3.2 创建项目
+ **URL**: `/api/v1/projects`
+ **Method**: `POST`

### 3.3 更新项目
+ **URL**: `/api/v1/projects/{projectId}`
+ **Method**: `PUT`

### 3.4 删除项目
+ **URL**: `/api/v1/projects/{projectId}`
+ **Method**: `DELETE`

## 4. Mock接口管理
### 4.1 获取接口列表
+ **URL**: `/api/v1/projects/{projectId}/apis`
+ **Method**: `GET`

### 4.2 创建接口
+ **URL**: `/api/v1/projects/{projectId}/apis`
+ **Method**: `POST`

### 4.3 更新接口
+ **URL**: `/api/v1/projects/{projectId}/apis/{apiId}`
+ **Method**: `PUT`

### 4.4 删除接口
+ **URL**: `/api/v1/projects/{projectId}/apis/{apiId}`
+ **Method**: `DELETE`

## 5. Mock数据调用
### 5.1 调用Mock接口
+ **URL**: `/mock/{projectCode}/{apiPath}`
+ **Method**: `{配置的请求方法}`
+ **说明**: 根据配置返回Mock数据

---

文档版本：V1.0  
更新日期：2024-01-01