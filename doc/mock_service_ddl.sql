-- Mock服务系统数据库DDL脚本
-- 数据库: MySQL 8.0
-- 字符集: utf8mb4
-- 排序规则: utf8mb4_general_ci
-- 存储引擎: InnoDB

-- 创建数据库
CREATE DATABASE IF NOT EXISTS mock_service
DEFAULT CHARACTER SET utf8mb4 
DEFAULT COLLATE utf8mb4_general_ci;

USE mock_service;

-- ========================================
-- 用户管理模块
-- ========================================

-- 用户表
CREATE TABLE sys_user (
    id BIGINT(20) NOT NULL COMMENT '用户ID',
    username VARCHAR(50) NOT NULL COMMENT '用户名',
    password VARCHAR(100) NOT NULL COMMENT '密码(加密)',
    nickname VARCHAR(50) DEFAULT NULL COMMENT '昵称',
    email VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
    mobile VARCHAR(20) DEFAULT NULL COMMENT '手机号',
    avatar VARCHAR(255) DEFAULT NULL COMMENT '头像URL',
    status TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-正常',
    last_login_time DATETIME DEFAULT NULL COMMENT '最后登录时间',
    last_login_ip VARCHAR(50) DEFAULT NULL COMMENT '最后登录IP',
    deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '删除标记',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    update_time DATETIME DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_username (username),
    UNIQUE KEY uk_email (email),
    UNIQUE KEY uk_mobile (mobile),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户表';

-- 角色表
CREATE TABLE sys_role (
    id BIGINT(20) NOT NULL COMMENT '角色ID',
    role_name VARCHAR(50) NOT NULL COMMENT '角色名称',
    role_code VARCHAR(50) NOT NULL COMMENT '角色编码',
    description VARCHAR(200) DEFAULT NULL COMMENT '描述',
    status TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态',
    deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '删除标记',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    update_time DATETIME DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_role_code (role_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='角色表';

-- 权限表
CREATE TABLE sys_permission (
    id BIGINT(20) NOT NULL COMMENT '权限ID',
    parent_id BIGINT(20) DEFAULT 0 COMMENT '父权限ID',
    permission_name VARCHAR(50) NOT NULL COMMENT '权限名称',
    permission_code VARCHAR(100) NOT NULL COMMENT '权限编码',
    permission_type TINYINT(1) NOT NULL COMMENT '类型:1-菜单,2-按钮,3-接口',
    path VARCHAR(200) DEFAULT NULL COMMENT '路径',
    icon VARCHAR(50) DEFAULT NULL COMMENT '图标',
    sort INT(11) DEFAULT 0 COMMENT '排序',
    status TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态',
    deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '删除标记',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_permission_code (permission_code),
    KEY idx_parent_id (parent_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='权限表';

-- 用户角色关联表
CREATE TABLE sys_user_role (
    id BIGINT(20) NOT NULL COMMENT 'ID',
    user_id BIGINT(20) NOT NULL COMMENT '用户ID',
    role_id BIGINT(20) NOT NULL COMMENT '角色ID',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_user_role (user_id, role_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户角色关联表';

-- 角色权限关联表
CREATE TABLE sys_role_permission (
    id BIGINT(20) NOT NULL COMMENT 'ID',
    role_id BIGINT(20) NOT NULL COMMENT '角色ID',
    permission_id BIGINT(20) NOT NULL COMMENT '权限ID',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_role_permission (role_id, permission_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='角色权限关联表';

-- ========================================
-- Mock业务模块
-- ========================================

-- Mock项目表
CREATE TABLE mock_project (
    id BIGINT(20) NOT NULL COMMENT '项目ID',
    project_name VARCHAR(100) NOT NULL COMMENT '项目名称',
    project_code VARCHAR(50) NOT NULL COMMENT '项目编码',
    description VARCHAR(500) DEFAULT NULL COMMENT '项目描述',
    project_type VARCHAR(20) DEFAULT NULL COMMENT '项目类型',
    base_url VARCHAR(200) DEFAULT NULL COMMENT '基础URL',
    owner_id BIGINT(20) NOT NULL COMMENT '负责人ID',
    status TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态',
    is_public TINYINT(1) DEFAULT 0 COMMENT '是否公开',
    deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '删除标记',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    update_time DATETIME DEFAULT NULL COMMENT '更新时间',
    create_by BIGINT(20) DEFAULT NULL COMMENT '创建人',
    update_by BIGINT(20) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (id),
    UNIQUE KEY uk_project_code (project_code),
    KEY idx_owner_id (owner_id),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Mock项目表';

-- API接口表
CREATE TABLE mock_api (
    id BIGINT(20) NOT NULL COMMENT '接口ID',
    project_id BIGINT(20) NOT NULL COMMENT '项目ID',
    group_id BIGINT(20) DEFAULT NULL COMMENT '分组ID',
    api_name VARCHAR(100) NOT NULL COMMENT '接口名称',
    api_path VARCHAR(500) NOT NULL COMMENT '接口路径',
    api_method VARCHAR(10) NOT NULL COMMENT '请求方法',
    api_version VARCHAR(20) DEFAULT 'v1' COMMENT '接口版本',
    description TEXT DEFAULT NULL COMMENT '接口描述',
    request_headers JSON DEFAULT NULL COMMENT '请求头配置',
    request_params JSON DEFAULT NULL COMMENT '请求参数配置',
    request_body JSON DEFAULT NULL COMMENT '请求体配置',
    response_headers JSON DEFAULT NULL COMMENT '响应头配置',
    response_body JSON DEFAULT NULL COMMENT '响应体配置',
    response_status INT(11) DEFAULT 200 COMMENT '响应状态码',
    response_delay INT(11) DEFAULT 0 COMMENT '响应延迟(ms)',
    mock_type VARCHAR(20) DEFAULT 'static' COMMENT 'Mock类型',
    status TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态',
    deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '删除标记',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    update_time DATETIME DEFAULT NULL COMMENT '更新时间',
    create_by BIGINT(20) DEFAULT NULL COMMENT '创建人',
    update_by BIGINT(20) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (id),
    UNIQUE KEY uk_project_path_method (project_id, api_path, api_method),
    KEY idx_project_id (project_id),
    KEY idx_group_id (group_id),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='API接口表';

-- Mock规则表
CREATE TABLE mock_rule (
    id BIGINT(20) NOT NULL COMMENT '规则ID',
    api_id BIGINT(20) NOT NULL COMMENT '接口ID',
    rule_name VARCHAR(100) NOT NULL COMMENT '规则名称',
    rule_type VARCHAR(20) NOT NULL COMMENT '规则类型',
    condition_type VARCHAR(20) DEFAULT NULL COMMENT '条件类型',
    condition_key VARCHAR(100) DEFAULT NULL COMMENT '条件键',
    condition_operator VARCHAR(20) DEFAULT NULL COMMENT '条件操作符',
    condition_value VARCHAR(500) DEFAULT NULL COMMENT '条件值',
    response_data JSON NOT NULL COMMENT '响应数据',
    response_status INT(11) DEFAULT 200 COMMENT '响应状态码',
    priority INT(11) DEFAULT 0 COMMENT '优先级',
    enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '删除标记',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    update_time DATETIME DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (id),
    KEY idx_api_id (api_id),
    KEY idx_enabled (enabled),
    KEY idx_priority (priority)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Mock规则表';

-- 接口分组表
CREATE TABLE mock_api_group (
    id BIGINT(20) NOT NULL COMMENT '分组ID',
    project_id BIGINT(20) NOT NULL COMMENT '项目ID',
    parent_id BIGINT(20) DEFAULT 0 COMMENT '父分组ID',
    group_name VARCHAR(100) NOT NULL COMMENT '分组名称',
    description VARCHAR(500) DEFAULT NULL COMMENT '分组描述',
    sort INT(11) DEFAULT 0 COMMENT '排序',
    deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '删除标记',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    update_time DATETIME DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (id),
    KEY idx_project_id (project_id),
    KEY idx_parent_id (parent_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='接口分组表';

-- ========================================
-- 日志模块
-- ========================================

-- 请求日志表
CREATE TABLE mock_request_log (
    id BIGINT(20) NOT NULL COMMENT '日志ID',
    project_id BIGINT(20) NOT NULL COMMENT '项目ID',
    api_id BIGINT(20) DEFAULT NULL COMMENT '接口ID',
    request_id VARCHAR(50) NOT NULL COMMENT '请求ID',
    request_method VARCHAR(10) NOT NULL COMMENT '请求方法',
    request_path VARCHAR(500) NOT NULL COMMENT '请求路径',
    request_headers JSON DEFAULT NULL COMMENT '请求头',
    request_params TEXT DEFAULT NULL COMMENT '请求参数',
    request_body TEXT DEFAULT NULL COMMENT '请求体',
    response_status INT(11) DEFAULT NULL COMMENT '响应状态码',
    response_headers JSON DEFAULT NULL COMMENT '响应头',
    response_body TEXT DEFAULT NULL COMMENT '响应体',
    response_time BIGINT(20) DEFAULT NULL COMMENT '响应时间(ms)',
    client_ip VARCHAR(50) DEFAULT NULL COMMENT '客户端IP',
    user_agent VARCHAR(500) DEFAULT NULL COMMENT 'User-Agent',
    error_message TEXT DEFAULT NULL COMMENT '错误信息',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    PRIMARY KEY (id),
    KEY idx_project_id (project_id),
    KEY idx_api_id (api_id),
    KEY idx_request_id (request_id),
    KEY idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='请求日志表';

-- 操作日志表
CREATE TABLE sys_operation_log (
    id BIGINT(20) NOT NULL COMMENT '日志ID',
    user_id BIGINT(20) DEFAULT NULL COMMENT '用户ID',
    username VARCHAR(50) DEFAULT NULL COMMENT '用户名',
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型',
    operation_desc VARCHAR(200) DEFAULT NULL COMMENT '操作描述',
    request_method VARCHAR(10) DEFAULT NULL COMMENT '请求方法',
    request_url VARCHAR(500) DEFAULT NULL COMMENT '请求URL',
    request_params TEXT DEFAULT NULL COMMENT '请求参数',
    response_data TEXT DEFAULT NULL COMMENT '响应数据',
    error_message TEXT DEFAULT NULL COMMENT '错误信息',
    operation_time BIGINT(20) DEFAULT NULL COMMENT '操作耗时(ms)',
    client_ip VARCHAR(50) DEFAULT NULL COMMENT '客户端IP',
    status TINYINT(1) DEFAULT NULL COMMENT '操作状态',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    PRIMARY KEY (id),
    KEY idx_user_id (user_id),
    KEY idx_operation_type (operation_type),
    KEY idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='操作日志表';

-- ========================================
-- 协作模块
-- ========================================

-- 项目成员表
CREATE TABLE mock_project_member (
    id BIGINT(20) NOT NULL COMMENT 'ID',
    project_id BIGINT(20) NOT NULL COMMENT '项目ID',
    user_id BIGINT(20) NOT NULL COMMENT '用户ID',
    role_type VARCHAR(20) NOT NULL COMMENT '角色类型',
    join_time DATETIME NOT NULL COMMENT '加入时间',
    invite_by BIGINT(20) DEFAULT NULL COMMENT '邀请人ID',
    PRIMARY KEY (id),
    UNIQUE KEY uk_project_user (project_id, user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='项目成员表';

-- 数据模板表
CREATE TABLE mock_data_template (
    id BIGINT(20) NOT NULL COMMENT '模板ID',
    project_id BIGINT(20) NOT NULL COMMENT '项目ID',
    template_name VARCHAR(100) NOT NULL COMMENT '模板名称',
    template_code VARCHAR(50) NOT NULL COMMENT '模板编码',
    description VARCHAR(500) DEFAULT NULL COMMENT '模板描述',
    template_schema JSON NOT NULL COMMENT '模板结构',
    template_data JSON DEFAULT NULL COMMENT '模板数据',
    version VARCHAR(20) DEFAULT 'v1' COMMENT '版本号',
    status TINYINT(1) DEFAULT 1 COMMENT '状态',
    deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '删除标记',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    update_time DATETIME DEFAULT NULL COMMENT '更新时间',
    create_by BIGINT(20) DEFAULT NULL COMMENT '创建人',
    update_by BIGINT(20) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (id),
    UNIQUE KEY uk_project_code (project_id, template_code),
    KEY idx_project_id (project_id),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数据模板表';

-- ========================================
-- 初始化数据
-- ========================================

-- 初始化角色
INSERT INTO sys_role (id, role_name, role_code, description, create_time) VALUES
(1, '超级管理员', 'ROLE_ADMIN', '系统所有权限', NOW()),
(2, '项目管理员', 'ROLE_PROJECT_ADMIN', '项目管理权限', NOW()),
(3, '开发人员', 'ROLE_DEVELOPER', '接口开发权限', NOW()),
(4, '测试人员', 'ROLE_TESTER', '接口测试权限', NOW()),
(5, '访客', 'ROLE_GUEST', '只读权限', NOW());

-- 初始化权限
INSERT INTO sys_permission (id, permission_name, permission_code, permission_type, path, create_time) VALUES
(1, '用户管理', 'user:manage', 1, '/users', NOW()),
(2, '项目管理', 'project:manage', 1, '/projects', NOW()),
(3, '接口管理', 'api:manage', 1, '/apis', NOW()),
(4, '日志查看', 'log:view', 1, '/logs', NOW()),
(5, '系统设置', 'system:config', 1, '/system', NOW());

-- 初始化管理员账号
INSERT INTO sys_user (id, username, password, nickname, email, status, create_time) VALUES
(1, 'admin', '$2a$10$encrypted_password', '系统管理员', '<EMAIL>', 1, NOW());

-- 分配管理员角色
INSERT INTO sys_user_role (id, user_id, role_id, create_time) VALUES
(1, 1, 1, NOW());

-- 分配管理员权限
INSERT INTO sys_role_permission (id, role_id, permission_id, create_time) VALUES
(1, 1, 1, NOW()),
(2, 1, 2, NOW()),
(3, 1, 3, NOW()),
(4, 1, 4, NOW()),
(5, 1, 5, NOW());