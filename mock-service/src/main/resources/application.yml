server:
  port: 8080
  servlet:
    context-path: /api

spring:
  datasource:
    # 数据库连接信息
    url: ********************************************************************************************************************************************************************************************
    username: TFonQJhoDmQubdd.root
    password: LH6PWmtn2Etgp7qI
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      # 连接池名称
      pool-name: MockServerHikariCP
      # 最小空闲连接数
      minimum-idle: 5
      # 最大连接池大小
      maximum-pool-size: 20
      # 连接超时时间(毫秒)
      connection-timeout: 30000
      # 空闲连接存活最大时间，默认600000（10分钟）
      idle-timeout: 600000
      # 连接最大存活时间，0表示永久存活，默认1800000（30分钟）
      max-lifetime: 1800000
      # 连接测试查询
      connection-test-query: SELECT 1

# MyBatis-Plus 配置
mybatis-plus:
  configuration:
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
    # 日志实现
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      # 主键策略
      id-type: auto
      # 逻辑删除字段
      logic-delete-field: deleted
      # 逻辑删除值
      logic-delete-value: 1
      # 逻辑未删除值
      logic-not-delete-value: 0
  # mapper文件位置
  mapper-locations: classpath*:/mapper/**/*.xml

# 日志配置
logging:
  level:
    cn.bughub: debug
    com.baomidou.mybatisplus: debug