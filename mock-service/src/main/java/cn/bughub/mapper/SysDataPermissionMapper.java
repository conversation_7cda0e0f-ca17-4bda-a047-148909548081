package cn.bughub.mapper;

import cn.bughub.entity.SysDataPermission;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 数据权限管理 Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-11
 */
@Mapper
public interface SysDataPermissionMapper extends BaseMapper<SysDataPermission> {

    /**
     * 根据表名查询数据权限规则
     * 
     * @param tableName 表名
     * @return 数据权限规则列表
     */
    @Select("SELECT * FROM sys_data_permission WHERE table_name = #{tableName} AND status = 1 AND deleted = 0")
    List<SysDataPermission> selectByTableName(@Param("tableName") String tableName);

    /**
     * 根据权限类型查询数据权限规则
     * 
     * @param permissionType 权限类型
     * @return 数据权限规则列表
     */
    @Select("SELECT * FROM sys_data_permission WHERE permission_type = #{permissionType} AND status = 1 AND deleted = 0")
    List<SysDataPermission> selectByPermissionType(@Param("permissionType") String permissionType);

    /**
     * 根据用户ID查询用户的数据权限规则
     * 
     * @param userId 用户ID
     * @return 数据权限规则列表
     */
    List<SysDataPermission> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据角色ID查询角色的数据权限规则
     * 
     * @param roleId 角色ID
     * @return 数据权限规则列表
     */
    @Select("SELECT dp.* FROM sys_data_permission dp " +
            "INNER JOIN sys_role_data_permission rdp ON dp.id = rdp.data_permission_id " +
            "WHERE rdp.role_id = #{roleId} AND dp.status = 1 AND dp.deleted = 0")
    List<SysDataPermission> selectByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据表名和权限类型查询数据权限规则
     * 
     * @param tableName 表名
     * @param permissionType 权限类型
     * @return 数据权限规则列表
     */
    @Select("SELECT * FROM sys_data_permission WHERE table_name = #{tableName} AND permission_type = #{permissionType} AND status = 1 AND deleted = 0")
    List<SysDataPermission> selectByTableAndType(@Param("tableName") String tableName, @Param("permissionType") String permissionType);

    /**
     * 根据用户ID和表名查询用户对该表的数据权限规则
     * 
     * @param userId 用户ID
     * @param tableName 表名
     * @return 数据权限规则列表
     */
    List<SysDataPermission> selectByUserIdAndTable(@Param("userId") Long userId, @Param("tableName") String tableName);

    /**
     * 根据用户ID、表名和权限类型查询数据权限规则
     * 
     * @param userId 用户ID
     * @param tableName 表名
     * @param permissionType 权限类型
     * @return 数据权限规则列表
     */
    List<SysDataPermission> selectByUserTableAndType(@Param("userId") Long userId, 
                                                     @Param("tableName") String tableName, 
                                                     @Param("permissionType") String permissionType);

    /**
     * 查询所有启用的数据权限规则
     * 
     * @return 启用的数据权限规则列表
     */
    @Select("SELECT * FROM sys_data_permission WHERE status = 1 AND deleted = 0 ORDER BY table_name, permission_type")
    List<SysDataPermission> selectEnabledRules();

    /**
     * 根据规则名称查询数据权限规则
     * 
     * @param ruleName 规则名称
     * @return 数据权限规则
     */
    @Select("SELECT * FROM sys_data_permission WHERE rule_name = #{ruleName} AND deleted = 0")
    SysDataPermission selectByRuleName(@Param("ruleName") String ruleName);

    /**
     * 查询指定表的所有列权限规则
     * 
     * @param tableName 表名
     * @return 列权限规则列表
     */
    @Select("SELECT * FROM sys_data_permission WHERE table_name = #{tableName} AND column_name IS NOT NULL AND status = 1 AND deleted = 0")
    List<SysDataPermission> selectColumnPermissions(@Param("tableName") String tableName);

    /**
     * 查询指定表的所有行权限规则
     * 
     * @param tableName 表名
     * @return 行权限规则列表
     */
    @Select("SELECT * FROM sys_data_permission WHERE table_name = #{tableName} AND filter_condition IS NOT NULL AND status = 1 AND deleted = 0")
    List<SysDataPermission> selectRowPermissions(@Param("tableName") String tableName);
}