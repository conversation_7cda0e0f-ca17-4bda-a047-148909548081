package cn.bughub.mapper;

import cn.bughub.entity.SysDepartment;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 部门管理 Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-11
 */
@Mapper
public interface SysDepartmentMapper extends BaseMapper<SysDepartment> {

    /**
     * 根据父部门ID查询子部门列表
     * 
     * @param parentId 父部门ID
     * @return 子部门列表
     */
    @Select("SELECT * FROM sys_department WHERE parent_id = #{parentId} AND deleted = 0 ORDER BY sort_order ASC")
    List<SysDepartment> selectByParentId(@Param("parentId") Long parentId);

    /**
     * 查询所有根部门（顶级部门）
     * 
     * @return 根部门列表
     */
    @Select("SELECT * FROM sys_department WHERE parent_id IS NULL AND deleted = 0 ORDER BY sort_order ASC")
    List<SysDepartment> selectRootDepartments();

    /**
     * 根据部门路径查询部门
     * 
     * @param path 部门路径
     * @return 部门信息
     */
    @Select("SELECT * FROM sys_department WHERE dept_path = #{path} AND deleted = 0")
    SysDepartment selectByPath(@Param("path") String path);

    /**
     * 查询指定部门的所有子部门（包括子部门的子部门）
     * 
     * @param deptId 部门ID
     * @return 所有子部门列表
     */
    @Select("SELECT * FROM sys_department WHERE dept_path LIKE CONCAT((SELECT dept_path FROM sys_department WHERE id = #{deptId}), '%') AND id != #{deptId} AND deleted = 0")
    List<SysDepartment> selectAllChildren(@Param("deptId") Long deptId);

    /**
     * 查询指定部门的所有父部门
     * 
     * @param deptId 部门ID
     * @return 所有父部门列表
     */
    List<SysDepartment> selectAllParents(@Param("deptId") Long deptId);

    /**
     * 根据用户ID查询用户所属部门
     * 
     * @param userId 用户ID
     * @return 用户所属部门列表
     */
    @Select("SELECT d.* FROM sys_department d " +
            "INNER JOIN sys_user_department ud ON d.id = ud.dept_id " +
            "WHERE ud.user_id = #{userId} AND d.deleted = 0")
    List<SysDepartment> selectByUserId(@Param("userId") Long userId);

    /**
     * 更新部门路径
     * 
     * @param id 部门ID
     * @param path 新路径
     * @return 更新行数
     */
    int updateDeptPath(@Param("id") Long id, @Param("path") String path);

    /**
     * 批量更新子部门路径
     * 
     * @param oldPath 旧路径前缀
     * @param newPath 新路径前缀
     * @return 更新行数
     */
    int updateChildrenPath(@Param("oldPath") String oldPath, @Param("newPath") String newPath);
}