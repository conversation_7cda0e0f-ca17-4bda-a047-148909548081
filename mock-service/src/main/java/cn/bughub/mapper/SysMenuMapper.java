package cn.bughub.mapper;

import cn.bughub.entity.SysMenu;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 菜单管理 Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-11
 */
@Mapper
public interface SysMenuMapper extends BaseMapper<SysMenu> {

    /**
     * 根据父菜单ID查询子菜单列表
     * 
     * @param parentId 父菜单ID
     * @return 子菜单列表
     */
    @Select("SELECT * FROM sys_menu WHERE parent_id = #{parentId} AND deleted = 0 ORDER BY sort_order ASC")
    List<SysMenu> selectByParentId(@Param("parentId") Long parentId);

    /**
     * 查询所有根菜单（顶级菜单）
     * 
     * @return 根菜单列表
     */
    @Select("SELECT * FROM sys_menu WHERE parent_id IS NULL AND deleted = 0 ORDER BY sort_order ASC")
    List<SysMenu> selectRootMenus();

    /**
     * 根据菜单类型查询菜单列表
     * 
     * @param menuType 菜单类型
     * @return 菜单列表
     */
    @Select("SELECT * FROM sys_menu WHERE menu_type = #{menuType} AND deleted = 0 ORDER BY sort_order ASC")
    List<SysMenu> selectByMenuType(@Param("menuType") Integer menuType);

    /**
     * 根据权限标识查询菜单
     * 
     * @param permission 权限标识
     * @return 菜单信息
     */
    @Select("SELECT * FROM sys_menu WHERE permission = #{permission} AND deleted = 0")
    SysMenu selectByPermission(@Param("permission") String permission);

    /**
     * 根据路由路径查询菜单
     * 
     * @param path 路由路径
     * @return 菜单信息
     */
    @Select("SELECT * FROM sys_menu WHERE path = #{path} AND deleted = 0")
    SysMenu selectByPath(@Param("path") String path);

    /**
     * 查询指定菜单的所有子菜单（包括子菜单的子菜单）
     * 
     * @param menuId 菜单ID
     * @return 所有子菜单列表
     */
    List<SysMenu> selectAllChildren(@Param("menuId") Long menuId);

    /**
     * 根据用户ID查询用户有权限的菜单列表
     * 
     * @param userId 用户ID
     * @return 用户菜单列表
     */
    List<SysMenu> selectMenusByUserId(@Param("userId") Long userId);

    /**
     * 根据角色ID查询角色菜单列表
     * 
     * @param roleId 角色ID
     * @return 角色菜单列表
     */
    @Select("SELECT m.* FROM sys_menu m " +
            "INNER JOIN sys_role_menu rm ON m.id = rm.menu_id " +
            "WHERE rm.role_id = #{roleId} AND m.deleted = 0 " +
            "ORDER BY m.sort_order ASC")
    List<SysMenu> selectMenusByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据用户ID查询用户有权限的按钮列表
     * 
     * @param userId 用户ID
     * @return 用户按钮权限列表
     */
    List<SysMenu> selectButtonsByUserId(@Param("userId") Long userId);

    /**
     * 查询所有启用的菜单
     * 
     * @return 启用的菜单列表
     */
    @Select("SELECT * FROM sys_menu WHERE status = 1 AND deleted = 0 ORDER BY sort_order ASC")
    List<SysMenu> selectEnabledMenus();

    /**
     * 根据组件名称查询菜单
     * 
     * @param component 组件名称
     * @return 菜单列表
     */
    @Select("SELECT * FROM sys_menu WHERE component = #{component} AND deleted = 0")
    List<SysMenu> selectByComponent(@Param("component") String component);

    /**
     * 查询用户的权限标识列表
     * 
     * @param userId 用户ID
     * @return 权限标识列表
     */
    List<String> selectPermissionsByUserId(@Param("userId") Long userId);

    /**
     * 检查菜单是否有子菜单
     * 
     * @param menuId 菜单ID
     * @return 子菜单数量
     */
    @Select("SELECT COUNT(*) FROM sys_menu WHERE parent_id = #{menuId} AND deleted = 0")
    int countChildren(@Param("menuId") Long menuId);
}