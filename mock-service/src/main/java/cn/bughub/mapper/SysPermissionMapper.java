package cn.bughub.mapper;

import cn.bughub.entity.SysPermission;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 权限Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Mapper
public interface SysPermissionMapper extends BaseMapper<SysPermission> {

    /**
     * 根据角色ID查询权限列表
     */
    @Select("SELECT p.* FROM sys_permission p " +
            "INNER JOIN sys_role_permission rp ON p.id = rp.permission_id " +
            "WHERE rp.role_id = #{roleId} AND p.deleted = 0 AND p.status = 1")
    List<SysPermission> selectPermissionsByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据用户ID查询权限列表
     */
    @Select("SELECT DISTINCT p.* FROM sys_permission p " +
            "INNER JOIN sys_role_permission rp ON p.id = rp.permission_id " +
            "INNER JOIN sys_user_role ur ON rp.role_id = ur.role_id " +
            "WHERE ur.user_id = #{userId} AND p.deleted = 0 AND p.status = 1")
    List<SysPermission> selectPermissionsByUserId(@Param("userId") Long userId);

    /**
     * 查询所有菜单权限
     */
    @Select("SELECT * FROM sys_permission WHERE permission_type = 1 AND deleted = 0 AND status = 1 ORDER BY sort")
    List<SysPermission> selectMenuPermissions();

    /**
     * 根据父ID查询子权限
     */
    @Select("SELECT * FROM sys_permission WHERE parent_id = #{parentId} AND deleted = 0 AND status = 1 ORDER BY sort")
    List<SysPermission> selectByParentId(@Param("parentId") Long parentId);

    /**
     * 根据权限编码查询权限
     */
    @Select("SELECT * FROM sys_permission WHERE permission_code = #{permissionCode} AND deleted = 0")
    SysPermission selectByPermissionCode(@Param("permissionCode") String permissionCode);
}