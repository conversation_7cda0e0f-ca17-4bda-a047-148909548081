package cn.bughub.mapper;

import cn.bughub.entity.SysRoleMenu;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 角色菜单关联 Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-11
 */
@Mapper
public interface SysRoleMenuMapper extends BaseMapper<SysRoleMenu> {

    /**
     * 根据角色ID查询菜单ID列表
     * 
     * @param roleId 角色ID
     * @return 菜单ID列表
     */
    @Select("SELECT menu_id FROM sys_role_menu WHERE role_id = #{roleId}")
    List<Long> selectMenuIdsByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据菜单ID查询角色ID列表
     * 
     * @param menuId 菜单ID
     * @return 角色ID列表
     */
    @Select("SELECT role_id FROM sys_role_menu WHERE menu_id = #{menuId}")
    List<Long> selectRoleIdsByMenuId(@Param("menuId") Long menuId);

    /**
     * 根据角色ID删除角色菜单关联
     * 
     * @param roleId 角色ID
     * @return 删除行数
     */
    @Delete("DELETE FROM sys_role_menu WHERE role_id = #{roleId}")
    int deleteByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据菜单ID删除角色菜单关联
     * 
     * @param menuId 菜单ID
     * @return 删除行数
     */
    @Delete("DELETE FROM sys_role_menu WHERE menu_id = #{menuId}")
    int deleteByMenuId(@Param("menuId") Long menuId);

    /**
     * 批量插入角色菜单关联
     * 
     * @param roleMenuList 角色菜单关联列表
     * @return 插入行数
     */
    int batchInsert(@Param("list") List<SysRoleMenu> roleMenuList);

    /**
     * 检查角色是否有指定菜单权限
     * 
     * @param roleId 角色ID
     * @param menuId 菜单ID
     * @return 是否存在关联
     */
    @Select("SELECT COUNT(*) FROM sys_role_menu WHERE role_id = #{roleId} AND menu_id = #{menuId}")
    int checkRoleMenuExists(@Param("roleId") Long roleId, @Param("menuId") Long menuId);

    /**
     * 根据用户ID查询用户的菜单ID列表（通过角色关联）
     * 
     * @param userId 用户ID
     * @return 菜单ID列表
     */
    @Select("SELECT DISTINCT rm.menu_id FROM sys_role_menu rm " +
            "INNER JOIN sys_user_role ur ON rm.role_id = ur.role_id " +
            "WHERE ur.user_id = #{userId}")
    List<Long> selectMenuIdsByUserId(@Param("userId") Long userId);

    /**
     * 根据角色ID列表查询菜单ID列表
     * 
     * @param roleIds 角色ID列表
     * @return 菜单ID列表
     */
    List<Long> selectMenuIdsByRoleIds(@Param("roleIds") List<Long> roleIds);

    /**
     * 根据菜单ID列表查询角色ID列表
     * 
     * @param menuIds 菜单ID列表
     * @return 角色ID列表
     */
    List<Long> selectRoleIdsByMenuIds(@Param("menuIds") List<Long> menuIds);

    /**
     * 批量删除角色菜单关联
     * 
     * @param roleId 角色ID
     * @param menuIds 菜单ID列表
     * @return 删除行数
     */
    int batchDeleteByRoleIdAndMenuIds(@Param("roleId") Long roleId, @Param("menuIds") List<Long> menuIds);

    /**
     * 根据角色ID和菜单类型查询菜单ID列表
     * 
     * @param roleId 角色ID
     * @param menuType 菜单类型
     * @return 菜单ID列表
     */
    @Select("SELECT rm.menu_id FROM sys_role_menu rm " +
            "INNER JOIN sys_menu m ON rm.menu_id = m.id " +
            "WHERE rm.role_id = #{roleId} AND m.menu_type = #{menuType} AND m.deleted = 0")
    List<Long> selectMenuIdsByRoleIdAndType(@Param("roleId") Long roleId, @Param("menuType") Integer menuType);
}