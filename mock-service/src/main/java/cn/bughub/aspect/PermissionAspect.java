package cn.bughub.aspect;

import cn.bughub.annotation.RequiresPermission;
import cn.bughub.security.UserPrincipal;
import cn.bughub.service.PermissionManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;

/**
 * 权限验证切面
 * 处理@RequiresPermission注解的权限验证
 * 
 * <AUTHOR>
 * @since 2025-01-11
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class PermissionAspect {

    private final PermissionManager permissionManager;

    /**
     * 权限验证切点
     */
    @Around("@annotation(cn.bughub.annotation.RequiresPermission)")
    public Object checkPermission(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取当前用户信息
        Long currentUserId = getCurrentUserId();
        if (currentUserId == null) {
            throw new AccessDeniedException("用户未登录");
        }

        // 获取方法签名和注解
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        RequiresPermission requiresPermission = method.getAnnotation(RequiresPermission.class);

        if (requiresPermission == null) {
            // 如果方法上没有注解，检查类上的注解
            requiresPermission = method.getDeclaringClass().getAnnotation(RequiresPermission.class);
        }

        if (requiresPermission != null) {
            // 执行权限验证
            boolean hasPermission = checkUserPermission(currentUserId, requiresPermission);
            
            if (!hasPermission) {
                String methodName = method.getDeclaringClass().getSimpleName() + "." + method.getName();
                log.warn("用户权限不足，用户ID: {}, 方法: {}, 需要权限: {}", 
                        currentUserId, methodName, String.join(",", requiresPermission.value()));
                
                String errorMessage = StringUtils.hasText(requiresPermission.message()) 
                        ? requiresPermission.message() 
                        : "权限不足，无法访问该资源";
                throw new AccessDeniedException(errorMessage);
            }
        }

        // 权限验证通过，执行目标方法
        return joinPoint.proceed();
    }

    /**
     * 获取当前登录用户ID
     */
    private Long getCurrentUserId() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.getPrincipal() instanceof UserPrincipal) {
                UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
                return userPrincipal.getId();
            }
        } catch (Exception e) {
            log.warn("获取当前用户信息失败", e);
        }
        return null;
    }

    /**
     * 检查用户权限
     */
    private boolean checkUserPermission(Long userId, RequiresPermission requiresPermission) {
        String[] permissions = requiresPermission.value();
        if (permissions == null || permissions.length == 0) {
            return true; // 没有指定权限要求，默认通过
        }

        RequiresPermission.Logical logical = requiresPermission.logical();
        
        switch (logical) {
            case AND:
                // 需要拥有所有权限
                return permissionManager.hasAllPermissions(userId, permissions);
            case OR:
                // 需要拥有任意一个权限
                return permissionManager.hasAnyPermission(userId, permissions);
            default:
                return false;
        }
    }
}