package cn.bughub.security;

import cn.bughub.entity.SysUser;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;
import java.util.List;

/**
 * Spring Security用户主体类
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@AllArgsConstructor
public class UserPrincipal implements UserDetails {

    private Long id;
    
    private String username;
    
    private String nickname;
    
    @JsonIgnore
    private String password;
    
    private String email;
    
    private String mobile;
    
    private String avatar;
    
    private Integer status;
    
    private Collection<? extends GrantedAuthority> authorities;

    /**
     * 从SysUser创建UserPrincipal
     */
    public static UserPrincipal create(SysUser user, List<GrantedAuthority> authorities) {
        return new UserPrincipal(
            user.getId(),
            user.getUsername(),
            user.getNickname(),
            user.getPassword(),
            user.getEmail(),
            user.getMobile(),
            user.getAvatar(),
            user.getStatus(),
            authorities
        );
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return authorities;
    }

    @Override
    public String getPassword() {
        return password;
    }

    @Override
    public String getUsername() {
        return username;
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return status == 1;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return status == 1;
    }
}