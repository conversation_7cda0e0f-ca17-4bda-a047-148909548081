package cn.bughub.common.utils;

import cn.bughub.common.response.PageResult;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 分页工具类
 *
 * <AUTHOR>
 * @date 2025/08/08
 */
public class PageUtils {

    /**
     * 默认页码
     */
    public static final int DEFAULT_PAGE_NUM = 1;

    /**
     * 默认每页大小
     */
    public static final int DEFAULT_PAGE_SIZE = 10;

    /**
     * 最大每页大小
     */
    public static final int MAX_PAGE_SIZE = 1000;

    /**
     * 创建MyBatis-Plus分页对象
     *
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @param <T>      数据类型
     * @return Page对象
     */
    public static <T> Page<T> buildPage(Integer pageNum, Integer pageSize) {
        return buildPage(pageNum, pageSize, true);
    }

    /**
     * 创建MyBatis-Plus分页对象
     *
     * @param pageNum     页码
     * @param pageSize    每页大小
     * @param searchCount 是否查询总数
     * @param <T>         数据类型
     * @return Page对象
     */
    public static <T> Page<T> buildPage(Integer pageNum, Integer pageSize, boolean searchCount) {
        // 参数校验和默认值设置
        int current = pageNum != null && pageNum > 0 ? pageNum : DEFAULT_PAGE_NUM;
        int size = pageSize != null && pageSize > 0 ? Math.min(pageSize, MAX_PAGE_SIZE) : DEFAULT_PAGE_SIZE;

        Page<T> page = new Page<>(current, size);
        page.setSearchCount(searchCount);
        return page;
    }

    /**
     * IPage转换为PageResult
     *
     * @param page IPage对象
     * @param <T>  数据类型
     * @return PageResult对象
     */
    public static <T> PageResult<T> toPageResult(IPage<T> page) {
        return PageResult.of(page);
    }

    /**
     * IPage转换为PageResult（带数据转换）
     *
     * @param page      IPage对象
     * @param converter 数据转换函数
     * @param <T>       源数据类型
     * @param <R>       目标数据类型
     * @return PageResult对象
     */
    public static <T, R> PageResult<R> toPageResult(IPage<T> page, Function<T, R> converter) {
        if (page == null || converter == null) {
            return PageResult.empty();
        }

        List<R> convertedList = page.getRecords() != null
                ? page.getRecords().stream().map(converter).collect(Collectors.toList())
                : Collections.emptyList();

        return PageResult.of(convertedList, page.getTotal(), (int) page.getCurrent(), (int) page.getSize());
    }

    /**
     * 构建分页查询参数
     *
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return 分页参数数组 [offset, limit]
     */
    public static int[] buildPageParams(Integer pageNum, Integer pageSize) {
        int current = pageNum != null && pageNum > 0 ? pageNum : DEFAULT_PAGE_NUM;
        int size = pageSize != null && pageSize > 0 ? Math.min(pageSize, MAX_PAGE_SIZE) : DEFAULT_PAGE_SIZE;

        int offset = (current - 1) * size;
        return new int[]{offset, size};
    }

    /**
     * 计算总页数
     *
     * @param total    总记录数
     * @param pageSize 每页大小
     * @return 总页数
     */
    public static int calculateTotalPages(long total, int pageSize) {
        if (total <= 0 || pageSize <= 0) {
            return 0;
        }
        return (int) Math.ceil((double) total / pageSize);
    }

    /**
     * 校验分页参数
     *
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return 校验后的分页参数数组 [pageNum, pageSize]
     */
    public static int[] validatePageParams(Integer pageNum, Integer pageSize) {
        int validPageNum = pageNum != null && pageNum > 0 ? pageNum : DEFAULT_PAGE_NUM;
        int validPageSize = pageSize != null && pageSize > 0 ? Math.min(pageSize, MAX_PAGE_SIZE) : DEFAULT_PAGE_SIZE;
        return new int[]{validPageNum, validPageSize};
    }

    /**
     * 判断是否有下一页
     *
     * @param pageNum   当前页码
     * @param pageSize  每页大小
     * @param total     总记录数
     * @return 是否有下一页
     */
    public static boolean hasNext(int pageNum, int pageSize, long total) {
        if (pageSize <= 0 || total <= 0) {
            return false;
        }
        int totalPages = calculateTotalPages(total, pageSize);
        return pageNum < totalPages;
    }

    /**
     * 判断是否有上一页
     *
     * @param pageNum 当前页码
     * @return 是否有上一页
     */
    public static boolean hasPrevious(int pageNum) {
        return pageNum > 1;
    }
}
