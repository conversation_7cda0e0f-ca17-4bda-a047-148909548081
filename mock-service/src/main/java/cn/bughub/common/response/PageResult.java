package cn.bughub.common.response;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * 分页响应结果封装类
 *
 * <AUTHOR>
 * @date 2025/08/08
 */
@Getter
@Setter
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PageResult<T> implements Serializable {
    
    /**
     * 构造函数
     */
    public PageResult() {
    }
    
    @Serial
    private static final long serialVersionUID = 1L;
    
    /**
     * 数据列表
     */
    private List<T> list;
    
    /**
     * 总记录数
     */
    private Long total;
    
    /**
     * 当前页码
     */
    private Integer pageNum;
    
    /**
     * 每页数量
     */
    private Integer pageSize;
    
    /**
     * 总页数
     */
    private Integer pages;
    
    /**
     * 是否有下一页
     */
    private Boolean hasNext;
    
    /**
     * 是否有上一页
     */
    private Boolean hasPrevious;
    
    /**
     * 构造函数
     */
    public PageResult(List<T> list, Long total, Integer pageNum, Integer pageSize) {
        this.list = list != null ? list : Collections.emptyList();
        this.total = total != null ? total : 0L;
        this.pageNum = pageNum != null && pageNum > 0 ? pageNum : 1;
        this.pageSize = pageSize != null && pageSize > 0 ? pageSize : 10;
        this.calculate();
    }
    
    /**
     * 静态构造方法
     */
    public static <T> PageResult<T> of(List<T> list, Long total, Integer pageNum, Integer pageSize) {
        return new PageResult<>(list, total, pageNum, pageSize);
    }
    
    /**
     * 从MyBatis-Plus的IPage转换
     */
    public static <T> PageResult<T> of(IPage<T> page) {
        if (page == null) {
            return new PageResult<>(Collections.emptyList(), 0L, 1, 10);
        }
        return new PageResult<>(page.getRecords(), page.getTotal(), (int) page.getCurrent(), (int) page.getSize());
    }
    
    /**
     * 创建空的分页结果
     */
    public static <T> PageResult<T> empty() {
        return new PageResult<>(Collections.emptyList(), 0L, 1, 10);
    }
    
    /**
     * 创建空的分页结果（指定分页参数）
     */
    public static <T> PageResult<T> empty(Integer pageNum, Integer pageSize) {
        return new PageResult<>(Collections.emptyList(), 0L, pageNum, pageSize);
    }
    
    /**
     * 计算分页信息
     */
    private void calculate() {
        // 确保基础数据不为空
        if (this.total == null) {
            this.total = 0L;
        }
        if (this.pageSize == null || this.pageSize <= 0) {
            this.pageSize = 10;
        }
        if (this.pageNum == null || this.pageNum <= 0) {
            this.pageNum = 1;
        }
        
        // 计算总页数
        if (this.total > 0 && this.pageSize > 0) {
            this.pages = (int) Math.ceil((double) this.total / this.pageSize);
        } else {
            this.pages = 0;
        }
        
        // 计算是否有上一页和下一页
        this.hasPrevious = this.pageNum > 1;
        this.hasNext = this.pageNum < this.pages;
    }
    
    /**
     * 获取起始位置（用于数据库查询）
     */
    public Integer getOffset() {
        if (pageNum != null && pageSize != null && pageNum > 0 && pageSize > 0) {
            return (pageNum - 1) * pageSize;
        }
        return 0;
    }
    
    /**
     * 获取结束位置（用于数据库查询）
     */
    public Integer getLimit() {
        return pageSize != null && pageSize > 0 ? pageSize : 10;
    }
    
    /**
     * 判断是否为空结果
     */
    public boolean isEmpty() {
        return list == null || list.isEmpty();
    }
    
    /**
     * 获取数据条数
     */
    public int getSize() {
        return list != null ? list.size() : 0;
    }
    
    /**
     * 判断是否为第一页
     */
    public boolean isFirst() {
        return pageNum != null && pageNum <= 1;
    }
    
    /**
     * 判断是否为最后一页
     */
    public boolean isLast() {
        return pageNum != null && pages != null && pageNum >= pages;
    }
    
}