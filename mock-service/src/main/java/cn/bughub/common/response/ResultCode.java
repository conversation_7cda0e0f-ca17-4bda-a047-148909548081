package cn.bughub.common.response;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应状态码枚举
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Getter
@AllArgsConstructor
public enum ResultCode {

    // 成功状态码
    SUCCESS(200, "操作成功"),
    FAILED(400, "操作失败"),
    CREATED(201, "创建成功"),
    ACCEPTED(202, "已接受"),
    NO_CONTENT(204, "无内容"),

    // 客户端错误状态码
    BAD_REQUEST(400, "请求参数错误"),
    VALIDATE_FAILED(400, "参数验证失败"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "方法不允许"),
    CONFLICT(409, "资源冲突"),
    UNPROCESSABLE_ENTITY(422, "无法处理的实体"),
    TOO_MANY_REQUESTS(429, "请求过于频繁"),

    // 服务器错误状态码
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),
    NOT_IMPLEMENTED(501, "功能未实现"),
    BAD_GATEWAY(502, "网关错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用"),
    GATEWAY_TIMEOUT(504, "网关超时"),

    // 业务错误状态码（1000-1999：用户相关）
    USER_NOT_FOUND(1001, "用户不存在"),
    USER_ALREADY_EXISTS(1002, "用户已存在"),
    USER_PASSWORD_ERROR(1003, "用户名或密码错误"),
    USER_ACCOUNT_DISABLED(1004, "账号已被禁用"),
    USER_ACCOUNT_LOCKED(1005, "账号已被锁定"),
    USER_ACCOUNT_EXPIRED(1006, "账号已过期"),
    USER_CREDENTIALS_EXPIRED(1007, "凭证已过期"),
    USER_NOT_LOGIN(1008, "用户未登录"),
    USER_LOGIN_EXPIRED(1009, "登录已过期"),
    USER_NO_PERMISSION(1010, "用户无权限"),
    LOGIN_FAILED(1011, "登录失败"),
    PASSWORD_NOT_MATCH(1012, "两次密码不一致"),
    EMAIL_ALREADY_EXISTS(1013, "邮箱已被使用"),

    // 业务错误状态码（2000-2999：认证相关）
    TOKEN_INVALID(2001, "令牌无效"),
    TOKEN_EXPIRED(2002, "令牌已过期"),
    TOKEN_NOT_FOUND(2003, "令牌不存在"),
    INVALID_TOKEN(2010, "无效的令牌"),
    REFRESH_TOKEN_INVALID(2004, "刷新令牌无效"),
    REFRESH_TOKEN_EXPIRED(2005, "刷新令牌已过期"),
    CAPTCHA_ERROR(2006, "验证码错误"),
    CAPTCHA_EXPIRED(2007, "验证码已过期"),
    SMS_CODE_ERROR(2008, "短信验证码错误"),
    EMAIL_CODE_ERROR(2009, "邮箱验证码错误"),

    // 业务错误状态码（3000-3999：角色权限相关）
    ROLE_NOT_FOUND(3001, "角色不存在"),
    ROLE_ALREADY_EXISTS(3002, "角色已存在"),
    ROLE_IN_USE(3003, "角色正在使用中"),
    PERMISSION_NOT_FOUND(3004, "权限不存在"),
    PERMISSION_ALREADY_EXISTS(3005, "权限已存在"),
    PERMISSION_DENIED(3006, "权限不足"),

    // 业务错误状态码（4000-4999：数据相关）
    DATA_NOT_FOUND(4001, "数据不存在"),
    DATA_ALREADY_EXISTS(4002, "数据已存在"),
    DATA_VALIDATION_ERROR(4003, "数据验证失败"),
    DATA_OPERATION_ERROR(4004, "数据操作失败"),
    DATA_FORMAT_ERROR(4005, "数据格式错误"),

    // 业务错误状态码（5000-5999：文件相关）
    FILE_NOT_FOUND(5001, "文件不存在"),
    FILE_UPLOAD_ERROR(5002, "文件上传失败"),
    FILE_DOWNLOAD_ERROR(5003, "文件下载失败"),
    FILE_DELETE_ERROR(5004, "文件删除失败"),
    FILE_FORMAT_ERROR(5005, "文件格式错误"),
    FILE_SIZE_EXCEED(5006, "文件大小超出限制"),

    // 业务错误状态码（6000-6999：第三方服务相关）
    THIRD_PARTY_ERROR(6001, "第三方服务错误"),
    SMS_SEND_ERROR(6002, "短信发送失败"),
    EMAIL_SEND_ERROR(6003, "邮件发送失败"),
    PAYMENT_ERROR(6004, "支付失败"),
    OAUTH_ERROR(6005, "OAuth认证失败"),

    // 业务错误状态码（9000-9999：系统相关）
    SYSTEM_BUSY(9001, "系统繁忙"),
    SYSTEM_MAINTENANCE(9002, "系统维护中"),
    OPERATION_TOO_FREQUENT(9003, "操作过于频繁"),
    ILLEGAL_OPERATION(9004, "非法操作"),
    PARAMETER_ERROR(9005, "参数错误"),
    BUSINESS_ERROR(9999, "业务异常");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态信息
     */
    private final String message;

    /**
     * 根据状态码获取枚举
     */
    public static ResultCode getByCode(Integer code) {
        for (ResultCode resultCode : values()) {
            if (resultCode.getCode().equals(code)) {
                return resultCode;
            }
        }
        return null;
    }

    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return this.code >= 200 && this.code < 300;
    }

    /**
     * 判断是否客户端错误
     */
    public boolean isClientError() {
        return this.code >= 400 && this.code < 500;
    }

    /**
     * 判断是否服务器错误
     */
    public boolean isServerError() {
        return this.code >= 500 && this.code < 600;
    }
}