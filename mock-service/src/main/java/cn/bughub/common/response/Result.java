package cn.bughub.common.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.ZoneOffset;

/**
 * 统一响应结果封装类
 *
 * <AUTHOR>
 * @date 2025/08/08
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Result<T> implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 响应码
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 时间戳
     */
    private Long timestamp;

    /**
     * 请求ID（用于链路追踪）
     */
    private String requestId;
    
    /**
     * 构造函数 - 自动设置时间戳
     */
    public Result(Integer code, String message) {
        this.code = code;
        this.message = message;
        this.timestamp = LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }

    /**
     * 构造函数 - 带数据
     */
    public Result(Integer code, String message, T data) {
        this(code, message);
        this.data = data;
    }

    /**
     * 初始化时间戳（用于无参构造函数后的初始化）
     */
    private void initTimestamp() {
        if (this.timestamp == null) {
            this.timestamp = LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli();
        }
    }
    
    /**
     * 成功响应（无数据）
     */
    public static <T> Result<T> success() {
        return new Result<>(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage());
    }

    /**
     * 成功响应（带数据）
     */
    public static <T> Result<T> success(T data) {
        return new Result<>(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), data);
    }

    /**
     * 成功响应（自定义消息）
     */
    public static <T> Result<T> success(String message) {
        return new Result<>(ResultCode.SUCCESS.getCode(), message);
    }

    /**
     * 成功响应（自定义消息和数据）
     */
    public static <T> Result<T> success(String message, T data) {
        return new Result<>(ResultCode.SUCCESS.getCode(), message, data);
    }
    
    /**
     * 失败响应
     */
    public static <T> Result<T> failed() {
        return new Result<>(ResultCode.FAILED.getCode(), ResultCode.FAILED.getMessage());
    }
    
    /**
     * 失败响应（自定义消息）
     */
    public static <T> Result<T> failed(String message) {
        return new Result<>(ResultCode.FAILED.getCode(), message);
    }
    
    /**
     * 失败响应（自定义错误码和消息）
     */
    public static <T> Result<T> failed(Integer code, String message) {
        return new Result<>(code, message);
    }
    
    /**
     * 失败响应（使用ResultCode枚举）
     */
    public static <T> Result<T> failed(ResultCode resultCode) {
        return new Result<>(resultCode.getCode(), resultCode.getMessage());
    }
    
    /**
     * 失败响应（使用ResultCode枚举，自定义消息）
     */
    public static <T> Result<T> failed(ResultCode resultCode, String message) {
        return new Result<>(resultCode.getCode(), message);
    }
    
    /**
     * 参数验证失败响应
     */
    public static <T> Result<T> validateFailed() {
        return new Result<>(ResultCode.VALIDATE_FAILED.getCode(), ResultCode.VALIDATE_FAILED.getMessage());
    }
    
    /**
     * 参数验证失败响应（自定义消息）
     */
    public static <T> Result<T> validateFailed(String message) {
        return new Result<>(ResultCode.VALIDATE_FAILED.getCode(), message);
    }
    
    /**
     * 未认证响应
     */
    public static <T> Result<T> unauthorized() {
        return new Result<>(ResultCode.UNAUTHORIZED.getCode(), ResultCode.UNAUTHORIZED.getMessage());
    }
    
    /**
     * 未认证响应（自定义消息）
     */
    public static <T> Result<T> unauthorized(String message) {
        return new Result<>(ResultCode.UNAUTHORIZED.getCode(), message);
    }
    
    /**
     * 未授权响应
     */
    public static <T> Result<T> forbidden() {
        return new Result<>(ResultCode.FORBIDDEN.getCode(), ResultCode.FORBIDDEN.getMessage());
    }
    
    /**
     * 未授权响应（自定义消息）
     */
    public static <T> Result<T> forbidden(String message) {
        return new Result<>(ResultCode.FORBIDDEN.getCode(), message);
    }
    
    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return ResultCode.SUCCESS.getCode().equals(this.code);
    }

    /**
     * 判断是否失败
     */
    public boolean isFailed() {
        return !isSuccess();
    }

    /**
     * 设置请求ID并返回自身（支持链式调用）
     */
    public Result<T> requestId(String requestId) {
        this.requestId = requestId;
        return this;
    }

    /**
     * 设置时间戳并返回自身（支持链式调用）
     */
    public Result<T> timestamp(Long timestamp) {
        this.timestamp = timestamp;
        return this;
    }

    /**
     * 创建一个新的Result实例并初始化时间戳
     */
    public static <T> Result<T> create() {
        Result<T> result = new Result<>();
        result.initTimestamp();
        return result;
    }

    /**
     * 创建一个新的Result实例并设置基本信息
     */
    public static <T> Result<T> create(Integer code, String message) {
        return new Result<>(code, message);
    }

    /**
     * 创建一个新的Result实例并设置完整信息
     */
    public static <T> Result<T> create(Integer code, String message, T data) {
        return new Result<>(code, message, data);
    }
}