package cn.bughub.common.exception;

import cn.bughub.common.response.Result;
import cn.bughub.common.response.ResultCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.NoHandlerFoundException;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DuplicateKeyException;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 *
 * <AUTHOR>
 * @date 2025/08/08
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 当前环境配置
     */
    @Value("${spring.profiles.active:dev}")
    private String activeProfile;
    
    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<?> handleBusinessException(BusinessException e, HttpServletRequest request) {
        logger.error("业务异常，请求路径：{}，异常信息：{}", request.getRequestURI(), e.getMessage());
        if (e.getData() != null) {
            return Result.failed(e.getCode(), e.getMessage()).setData(e.getData());
        }
        return Result.failed(e.getCode(), e.getMessage());
    }
    
    /**
     * 处理参数验证异常 - @RequestBody参数验证失败
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<?> handleMethodArgumentNotValidException(MethodArgumentNotValidException e, HttpServletRequest request) {
        logger.error("参数验证失败，请求路径：{}，异常信息：{}", request.getRequestURI(), e.getMessage());
        BindingResult bindingResult = e.getBindingResult();
        Map<String, String> errors = new HashMap<>();
        
        for (FieldError fieldError : bindingResult.getFieldErrors()) {
            errors.put(fieldError.getField(), fieldError.getDefaultMessage());
        }
        
        String message = errors.entrySet().stream()
                .map(entry -> entry.getKey() + ": " + entry.getValue())
                .collect(Collectors.joining(", "));
        
        return Result.validateFailed(message).setData(errors);
    }
    
    /**
     * 处理参数绑定异常 - 表单参数验证失败
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<?> handleBindException(BindException e, HttpServletRequest request) {
        logger.error("参数绑定失败，请求路径：{}，异常信息：{}", request.getRequestURI(), e.getMessage());
        BindingResult bindingResult = e.getBindingResult();
        Map<String, String> errors = new HashMap<>();
        
        for (FieldError fieldError : bindingResult.getFieldErrors()) {
            errors.put(fieldError.getField(), fieldError.getDefaultMessage());
        }
        
        String message = errors.entrySet().stream()
                .map(entry -> entry.getKey() + ": " + entry.getValue())
                .collect(Collectors.joining(", "));
        
        return Result.validateFailed(message).setData(errors);
    }
    
    /**
     * 处理约束违反异常 - @RequestParam参数验证失败
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<?> handleConstraintViolationException(ConstraintViolationException e, HttpServletRequest request) {
        logger.error("约束违反异常，请求路径：{}，异常信息：{}", request.getRequestURI(), e.getMessage());
        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
        Map<String, String> errors = new HashMap<>();
        
        for (ConstraintViolation<?> violation : violations) {
            String propertyPath = violation.getPropertyPath().toString();
            String message = violation.getMessage();
            errors.put(propertyPath, message);
        }
        
        String message = errors.entrySet().stream()
                .map(entry -> entry.getKey() + ": " + entry.getValue())
                .collect(Collectors.joining(", "));
        
        return Result.validateFailed(message).setData(errors);
    }
    
    /**
     * 处理缺少请求参数异常
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<?> handleMissingServletRequestParameterException(MissingServletRequestParameterException e, HttpServletRequest request) {
        logger.error("缺少请求参数，请求路径：{}，异常信息：{}", request.getRequestURI(), e.getMessage());
        String message = String.format("缺少必需的请求参数：%s", e.getParameterName());
        return Result.validateFailed(message);
    }
    
    /**
     * 处理参数类型不匹配异常
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<?> handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e, HttpServletRequest request) {
        logger.error("参数类型不匹配，请求路径：{}，异常信息：{}", request.getRequestURI(), e.getMessage());
        String message = String.format("参数类型不匹配：%s", e.getName());
        return Result.validateFailed(message);
    }
    
    /**
     * 处理HTTP消息不可读异常
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<?> handleHttpMessageNotReadableException(HttpMessageNotReadableException e, HttpServletRequest request) {
        logger.error("请求体不可读，请求路径：{}，异常信息：{}", request.getRequestURI(), e.getMessage());
        return Result.validateFailed("请求体格式错误或无法解析");
    }
    
    /**
     * 处理不支持的HTTP方法异常
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
    public Result<?> handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e, HttpServletRequest request) {
        logger.error("不支持的请求方法，请求路径：{}，异常信息：{}", request.getRequestURI(), e.getMessage());
        String message = String.format("不支持的请求方法：%s，支持的方法：%s", 
                e.getMethod(), 
                String.join(", ", e.getSupportedMethods() != null ? e.getSupportedMethods() : new String[]{}));
        return Result.failed(ResultCode.METHOD_NOT_ALLOWED, message);
    }
    
    /**
     * 处理不支持的媒体类型异常
     */
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    @ResponseStatus(HttpStatus.UNSUPPORTED_MEDIA_TYPE)
    public Result<?> handleHttpMediaTypeNotSupportedException(HttpMediaTypeNotSupportedException e, HttpServletRequest request) {
        logger.error("不支持的媒体类型，请求路径：{}，异常信息：{}", request.getRequestURI(), e.getMessage());
        return Result.failed(415, "不支持的媒体类型");
    }
    
    /**
     * 处理404异常
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public Result<?> handleNoHandlerFoundException(NoHandlerFoundException e, HttpServletRequest request) {
        logger.error("请求的资源不存在，请求路径：{}，异常信息：{}", request.getRequestURI(), e.getMessage());
        return Result.failed(ResultCode.NOT_FOUND);
    }
    
    /**
     * 处理空指针异常
     */
    @ExceptionHandler(NullPointerException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<?> handleNullPointerException(NullPointerException e, HttpServletRequest request) {
        logger.error("空指针异常，请求路径：{}，异常信息：", request.getRequestURI(), e);
        return Result.failed("系统内部错误，请联系管理员");
    }
    
    /**
     * 处理其他未知异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<?> handleException(Exception e, HttpServletRequest request) {
        logger.error("系统异常，请求路径：{}，异常信息：", request.getRequestURI(), e);
        
        // 生产环境不要返回详细的错误信息
        String message = "系统繁忙，请稍后重试";

        // 开发环境可以返回详细错误信息，方便调试
        if (isDevelopmentEnvironment()) {
            message = e.getMessage() != null ? e.getMessage() : "系统内部错误";
        }
        
        return Result.failed(message);
    }
    
    /**
     * 处理数据库异常
     */
    @ExceptionHandler(DataAccessException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<?> handleDataAccessException(DataAccessException e, HttpServletRequest request) {
        logger.error("数据库访问异常，请求路径：{}，异常信息：", request.getRequestURI(), e);

        if (e instanceof DuplicateKeyException) {
            return Result.failed("数据已存在，请检查后重试");
        }

        String message = isDevelopmentEnvironment() ? "数据库访问异常：" + e.getMessage() : "数据访问异常，请稍后重试";
        return Result.failed(message);
    }

    /**
     * 处理SQL异常
     */
    @ExceptionHandler(SQLException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<?> handleSQLException(SQLException e, HttpServletRequest request) {
        logger.error("SQL异常，请求路径：{}，异常信息：", request.getRequestURI(), e);

        String message = isDevelopmentEnvironment() ? "SQL异常：" + e.getMessage() : "数据处理异常，请稍后重试";
        return Result.failed(message);
    }

    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<?> handleRuntimeException(RuntimeException e, HttpServletRequest request) {
        logger.error("运行时异常，请求路径：{}，异常信息：", request.getRequestURI(), e);

        // 如果是包装的业务异常，则按业务异常处理
        Throwable cause = e.getCause();
        if (cause instanceof BusinessException) {
            BusinessException businessException = (BusinessException) cause;
            return Result.failed(businessException.getCode(), businessException.getMessage());
        }

        return Result.failed("系统运行异常，请稍后重试");
    }

    /**
     * 判断是否为开发环境
     */
    private boolean isDevelopmentEnvironment() {
        return "dev".equals(activeProfile) || "test".equals(activeProfile);
    }
}