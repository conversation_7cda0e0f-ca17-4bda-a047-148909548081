package cn.bughub.vo;

import lombok.Data;

import java.util.List;

/**
 * 菜单视图对象
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
public class MenuVO {

    /**
     * 菜单ID
     */
    private Long id;

    /**
     * 父菜单ID
     */
    private Long parentId;

    /**
     * 菜单名称
     */
    private String menuName;

    /**
     * 菜单编码
     */
    private String menuCode;

    /**
     * 菜单类型：1-目录，2-菜单，3-按钮
     */
    private Integer menuType;

    /**
     * 路由路径
     */
    private String path;

    /**
     * 组件路径
     */
    private String component;

    /**
     * 菜单图标
     */
    private String icon;

    /**
     * 显示顺序
     */
    private Integer sortOrder;

    /**
     * 是否显示：0-隐藏，1-显示
     */
    private Integer visible;

    /**
     * 权限标识
     */
    private String perms;

    /**
     * 路由参数
     */
    private String queryParams;

    /**
     * 是否外链：0-否，1-是
     */
    private Integer isFrame;

    /**
     * 是否缓存：0-不缓存，1-缓存
     */
    private Integer isCache;

    /**
     * 子菜单列表
     */
    private List<MenuVO> children;

    /**
     * 菜单层级
     */
    private Integer level;

    /**
     * 菜单路径
     */
    private String menuPath;

    /**
     * 是否选中
     */
    private Boolean checked;

    /**
     * 菜单元数据
     */
    private MenuMeta meta;

    /**
     * 菜单元数据内部类
     */
    @Data
    public static class MenuMeta {
        /**
         * 菜单标题
         */
        private String title;

        /**
         * 菜单图标
         */
        private String icon;

        /**
         * 是否隐藏
         */
        private Boolean hidden;

        /**
         * 是否缓存
         */
        private Boolean keepAlive;

        /**
         * 权限标识
         */
        private String[] permissions;

        /**
         * 角色标识
         */
        private String[] roles;

        /**
         * 是否固定标签页
         */
        private Boolean affix;

        /**
         * 外链地址
         */
        private String link;

        /**
         * 是否全屏
         */
        private Boolean fullscreen;
    }
}