package cn.bughub.service;

import cn.bughub.entity.SysMenu;
import cn.bughub.vo.MenuVO;
import cn.bughub.dto.DataPermissionContext;

import java.util.List;
import java.util.Set;

/**
 * 权限管理器接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface PermissionManager {

    /**
     * 获取用户菜单权限
     * 
     * @param userId 用户ID
     * @return 菜单列表
     */
    List<MenuVO> getUserMenus(Long userId);

    /**
     * 获取用户按钮权限
     * 
     * @param userId 用户ID
     * @return 按钮权限标识集合
     */
    Set<String> getUserButtons(Long userId);

    /**
     * 获取用户数据权限
     * 
     * @param userId 用户ID
     * @return 数据权限上下文
     */
    DataPermissionContext getUserDataPermission(Long userId);

    /**
     * 检查用户是否有指定权限
     * 
     * @param userId 用户ID
     * @param permission 权限标识
     * @return 是否有权限
     */
    boolean hasPermission(Long userId, String permission);

    /**
     * 检查用户是否有指定菜单权限
     * 
     * @param userId 用户ID
     * @param menuId 菜单ID
     * @return 是否有权限
     */
    boolean hasMenuPermission(Long userId, Long menuId);

    /**
     * 检查用户是否有指定数据权限
     * 
     * @param userId 用户ID
     * @param resourceType 资源类型
     * @param resourceName 资源名称
     * @param permissionType 权限类型
     * @return 是否有权限
     */
    boolean hasDataPermission(Long userId, String resourceType, String resourceName, Integer permissionType);

    /**
     * 刷新用户权限缓存
     * 
     * @param userId 用户ID
     */
    void refreshUserPermission(Long userId);

    /**
     * 刷新角色权限缓存
     * 
     * @param roleId 角色ID
     */
    void refreshRolePermission(Long roleId);

    /**
     * 清除所有权限缓存
     */
    void clearAllPermissionCache();

    /**
     * 获取菜单树
     * 
     * @param userId 用户ID
     * @param menuType 菜单类型
     * @return 菜单树
     */
    List<SysMenu> getMenuTree(Long userId, Integer menuType);

    /**
     * 构建菜单树
     * 
     * @param menus 菜单列表
     * @param parentId 父菜单ID
     * @return 菜单树
     */
    List<SysMenu> buildMenuTree(List<SysMenu> menus, Long parentId);

    /**
     * 获取用户所有权限标识
     * 
     * @param userId 用户ID
     * @return 权限标识集合
     */
    Set<String> getUserPermissions(Long userId);

    /**
     * 获取角色所有权限标识
     * 
     * @param roleId 角色ID
     * @return 权限标识集合
     */
    Set<String> getRolePermissions(Long roleId);

    /**
     * 预加载用户权限到缓存
     * 
     * @param userId 用户ID
     */
    void preloadUserPermissions(Long userId);

    /**
     * 检查权限是否过期
     * 
     * @param userId 用户ID
     * @return 是否过期
     */
    boolean isPermissionExpired(Long userId);
}