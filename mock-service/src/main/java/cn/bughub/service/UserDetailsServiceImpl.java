package cn.bughub.service;

import cn.bughub.entity.SysPermission;
import cn.bughub.entity.SysRole;
import cn.bughub.entity.SysUser;
import cn.bughub.mapper.SysPermissionMapper;
import cn.bughub.mapper.SysRoleMapper;
import cn.bughub.mapper.SysUserMapper;
import cn.bughub.security.UserPrincipal;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户详情服务实现类
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserDetailsServiceImpl implements UserDetailsService {

    private final SysUserMapper userMapper;
    private final SysRoleMapper roleMapper;
    private final SysPermissionMapper permissionMapper;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        // 查询用户信息
        QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("username", username)
                   .eq("deleted", 0)
                   .eq("status", 1);
        
        SysUser user = userMapper.selectOne(queryWrapper);
        
        if (user == null) {
            throw new UsernameNotFoundException("用户不存在: " + username);
        }

        // 获取用户角色
        List<SysRole> roles = roleMapper.selectRolesByUserId(user.getId());
        
        // 获取用户权限
        List<SysPermission> permissions = new ArrayList<>();
        for (SysRole role : roles) {
            List<SysPermission> rolePermissions = permissionMapper.selectPermissionsByRoleId(role.getId());
            permissions.addAll(rolePermissions);
        }
        
        // 去重
        permissions = permissions.stream().distinct().collect(Collectors.toList());
        
        // 构建权限列表
        List<GrantedAuthority> authorities = new ArrayList<>();
        
        // 添加角色权限（ROLE_前缀）
        for (SysRole role : roles) {
            authorities.add(new SimpleGrantedAuthority("ROLE_" + role.getRoleCode()));
        }
        
        // 添加具体权限
        for (SysPermission permission : permissions) {
            authorities.add(new SimpleGrantedAuthority(permission.getPermissionCode()));
        }

        // 设置用户的角色和权限信息
        user.setRoles(roles);
        user.setPermissions(permissions);

        // 创建UserPrincipal对象
        return UserPrincipal.create(user, authorities);
    }

    /**
     * 根据用户ID加载用户信息
     */
    public UserDetails loadUserById(Long userId) {
        SysUser user = userMapper.selectById(userId);
        
        if (user == null || user.getDeleted() == 1) {
            throw new UsernameNotFoundException("用户不存在: " + userId);
        }

        // 获取用户角色
        List<SysRole> roles = roleMapper.selectRolesByUserId(user.getId());
        
        // 获取用户权限
        List<SysPermission> permissions = new ArrayList<>();
        for (SysRole role : roles) {
            List<SysPermission> rolePermissions = permissionMapper.selectPermissionsByRoleId(role.getId());
            permissions.addAll(rolePermissions);
        }
        
        // 去重
        permissions = permissions.stream().distinct().collect(Collectors.toList());
        
        // 构建权限列表
        List<GrantedAuthority> authorities = new ArrayList<>();
        
        // 添加角色权限
        for (SysRole role : roles) {
            authorities.add(new SimpleGrantedAuthority("ROLE_" + role.getRoleCode()));
        }
        
        // 添加具体权限
        for (SysPermission permission : permissions) {
            authorities.add(new SimpleGrantedAuthority(permission.getPermissionCode()));
        }

        user.setRoles(roles);
        user.setPermissions(permissions);

        return UserPrincipal.create(user, authorities);
    }
}