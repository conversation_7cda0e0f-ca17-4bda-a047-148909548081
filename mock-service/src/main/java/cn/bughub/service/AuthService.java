package cn.bughub.service;

import cn.bughub.common.exception.BusinessException;
import cn.bughub.common.response.ResultCode;
import cn.bughub.dto.auth.LoginRequest;
import cn.bughub.dto.auth.LoginResponse;
import cn.bughub.dto.auth.RegisterRequest;
import cn.bughub.entity.SysUser;
import cn.bughub.mapper.SysUserMapper;
import cn.bughub.security.jwt.JwtUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 认证服务
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthService {

    private final AuthenticationManager authenticationManager;
    private final JwtUtils jwtUtils;
    private final SysUserMapper userMapper;
    private final PasswordEncoder passwordEncoder;

    // 用于存储已注销的token（实际项目中应使用Redis）
    private final Map<String, LocalDateTime> blacklistedTokens = new ConcurrentHashMap<>();

    /**
     * 用户登录
     */
    public LoginResponse login(LoginRequest request, String clientIp) {
        try {
            // 认证用户
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(
                            request.getUsername(),
                            request.getPassword()
                    )
            );

            SecurityContextHolder.getContext().setAuthentication(authentication);

            // 获取用户信息
            SysUser user = userMapper.selectByUsername(request.getUsername());
            if (user == null) {
                throw new BusinessException(ResultCode.USER_NOT_FOUND);
            }

            // 更新登录信息
            user.setLastLoginTime(LocalDateTime.now());
            user.setLastLoginIp(clientIp);
            userMapper.updateById(user);

            // 生成令牌
            String accessToken = jwtUtils.generateAccessToken(user);
            String refreshToken = jwtUtils.generateRefreshToken(user);

            // 构建响应
            LoginResponse response = new LoginResponse();
            response.setAccessToken(accessToken);
            response.setRefreshToken(refreshToken);
            response.setTokenType("Bearer");
            response.setExpiresIn(7200L); // 2小时

            // 设置用户信息
            LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo();
            userInfo.setId(user.getId());
            userInfo.setUsername(user.getUsername());
            userInfo.setNickname(user.getNickname());
            userInfo.setEmail(user.getEmail());
            userInfo.setMobile(user.getMobile());
            userInfo.setAvatar(user.getAvatar());
            // 简化处理，实际应从数据库获取角色
            // userInfo.setRoles(roles); // TODO: 从数据库获取角色列表
            response.setUserInfo(userInfo);

            log.info("用户登录成功: username={}, ip={}", request.getUsername(), clientIp);
            return response;

        } catch (Exception e) {
            log.error("用户登录失败: username={}, error={}", request.getUsername(), e.getMessage());
            throw new BusinessException(ResultCode.LOGIN_FAILED);
        }
    }

    /**
     * 用户注册
     */
    @Transactional
    public void register(RegisterRequest request) {
        // 验证密码一致性
        if (!request.getPassword().equals(request.getConfirmPassword())) {
            throw new BusinessException(ResultCode.PASSWORD_NOT_MATCH);
        }

        // 检查用户名是否存在
        if (userMapper.selectByUsername(request.getUsername()) != null) {
            throw new BusinessException(ResultCode.USER_ALREADY_EXISTS);
        }

        // 检查邮箱是否存在
        if (request.getEmail() != null && userMapper.selectByEmail(request.getEmail()) != null) {
            throw new BusinessException(ResultCode.EMAIL_ALREADY_EXISTS);
        }

        // 创建用户
        SysUser user = new SysUser();
        user.setUsername(request.getUsername());
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setEmail(request.getEmail());
        user.setMobile(request.getMobile());
        user.setNickname(request.getNickname() != null ? request.getNickname() : request.getUsername());
        user.setStatus(1); // 启用状态
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());

        userMapper.insert(user);
        log.info("用户注册成功: username={}", request.getUsername());
    }

    /**
     * 刷新令牌
     */
    public LoginResponse refreshToken(String refreshToken) {
        try {
            // 验证刷新令牌
            if (!jwtUtils.validateToken(refreshToken)) {
                throw new BusinessException(ResultCode.INVALID_TOKEN);
            }

            // 检查令牌类型
            String tokenType = jwtUtils.getTokenType(refreshToken);
            if (!"refresh".equals(tokenType)) {
                throw new BusinessException(ResultCode.INVALID_TOKEN);
            }

            // 获取用户信息
            Long userId = jwtUtils.getUserIdFromToken(refreshToken);
            SysUser user = userMapper.selectById(userId);
            if (user == null) {
                throw new BusinessException(ResultCode.USER_NOT_FOUND);
            }

            // 生成新的访问令牌
            String newAccessToken = jwtUtils.generateAccessToken(user);

            // 构建响应
            LoginResponse response = new LoginResponse();
            response.setAccessToken(newAccessToken);
            response.setRefreshToken(refreshToken); // 保持原刷新令牌
            response.setTokenType("Bearer");
            response.setExpiresIn(7200L);

            log.info("令牌刷新成功: userId={}", userId);
            return response;

        } catch (Exception e) {
            log.error("令牌刷新失败: error={}", e.getMessage());
            throw new BusinessException(ResultCode.INVALID_TOKEN);
        }
    }

    /**
     * 用户登出
     */
    public void logout(String token) {
        // 将token加入黑名单
        blacklistedTokens.put(token, LocalDateTime.now());

        // 清理过期的黑名单token（简化处理）
        cleanupBlacklistedTokens();

        log.info("用户登出成功");
    }

    /**
     * 获取当前用户信息
     */
    public LoginResponse.UserInfo getCurrentUserInfo(Long userId) {
        SysUser user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo();
        userInfo.setId(user.getId());
        userInfo.setUsername(user.getUsername());
        userInfo.setNickname(user.getNickname());
        userInfo.setEmail(user.getEmail());
        userInfo.setMobile(user.getMobile());
        userInfo.setAvatar(user.getAvatar());
        // 简化处理，实际应从数据库获取角色

        return userInfo;
    }

    /**
     * 发送验证码
     */
    public void sendVerificationCode(String target, String type) {
        // TODO: 实现验证码发送逻辑
        log.info("发送验证码: target={}, type={}", target, type);
    }

    /**
     * 忘记密码
     */
    public void forgotPassword(String account) {
        // TODO: 实现忘记密码逻辑
        log.info("忘记密码请求: account={}", account);
    }

    /**
     * 重置密码
     */
    public void resetPassword(String token, String newPassword) {
        // TODO: 实现重置密码逻辑
        log.info("重置密码请求");
    }

    /**
     * 验证邮箱
     */
    public void verifyEmail(String token) {
        // TODO: 实现邮箱验证逻辑
        log.info("验证邮箱请求");
    }

    /**
     * 检查token是否在黑名单中
     */
    public boolean isTokenBlacklisted(String token) {
        return blacklistedTokens.containsKey(token);
    }

    /**
     * 清理过期的黑名单token
     */
    private void cleanupBlacklistedTokens() {
        LocalDateTime cutoff = LocalDateTime.now().minusHours(24);
        blacklistedTokens.entrySet().removeIf(entry -> entry.getValue().isBefore(cutoff));
    }

}