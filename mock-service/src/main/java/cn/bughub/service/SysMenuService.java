package cn.bughub.service;

import cn.bughub.entity.SysMenu;
import cn.bughub.vo.MenuVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 菜单管理服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-11
 */
public interface SysMenuService extends IService<SysMenu> {

    /**
     * 获取菜单树列表
     * 
     * @return 菜单树列表
     */
    List<MenuVO> getMenuTree();

    /**
     * 根据用户ID获取菜单树
     * 
     * @param userId 用户ID
     * @return 用户菜单树
     */
    List<MenuVO> getUserMenuTree(Long userId);

    /**
     * 根据父菜单ID获取子菜单列表
     * 
     * @param parentId 父菜单ID
     * @return 子菜单列表
     */
    List<SysMenu> getMenusByParentId(Long parentId);

    /**
     * 创建菜单
     * 
     * @param menu 菜单信息
     * @return 是否成功
     */
    boolean createMenu(SysMenu menu);

    /**
     * 更新菜单
     * 
     * @param menu 菜单信息
     * @return 是否成功
     */
    boolean updateMenu(SysMenu menu);

    /**
     * 删除菜单
     * 
     * @param menuId 菜单ID
     * @return 是否成功
     */
    boolean deleteMenu(Long menuId);

    /**
     * 批量删除菜单
     * 
     * @param menuIds 菜单ID列表
     * @return 是否成功
     */
    boolean batchDeleteMenus(List<Long> menuIds);

    /**
     * 检查菜单是否有子菜单
     * 
     * @param menuId 菜单ID
     * @return 是否有子菜单
     */
    boolean hasChildren(Long menuId);

    /**
     * 根据权限标识查询菜单
     * 
     * @param permission 权限标识
     * @return 菜单信息
     */
    SysMenu getMenuByPermission(String permission);

    /**
     * 根据路由路径查询菜单
     * 
     * @param path 路由路径
     * @return 菜单信息
     */
    SysMenu getMenuByPath(String path);

    /**
     * 获取所有启用的菜单
     * 
     * @return 启用的菜单列表
     */
    List<SysMenu> getEnabledMenus();

    /**
     * 根据菜单类型获取菜单列表
     * 
     * @param menuType 菜单类型
     * @return 菜单列表
     */
    List<SysMenu> getMenusByType(Integer menuType);

    /**
     * 更新菜单状态
     * 
     * @param menuId 菜单ID
     * @param status 状态
     * @return 是否成功
     */
    boolean updateMenuStatus(Long menuId, Integer status);
}