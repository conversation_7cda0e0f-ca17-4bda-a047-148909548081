package cn.bughub.service.impl;

import cn.bughub.dto.DataPermissionContext;
import cn.bughub.entity.*;
import cn.bughub.mapper.*;
import cn.bughub.service.PermissionManager;
import cn.bughub.vo.MenuVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 权限管理服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-11
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PermissionManagerImpl implements PermissionManager {

    private final SysMenuMapper menuMapper;
    private final SysUserMapper userMapper;
    private final SysRoleMapper roleMapper;
    private final SysRoleMenuMapper roleMenuMapper;
    private final SysDataPermissionMapper dataPermissionMapper;
    private final SysDepartmentMapper departmentMapper;

    @Override
    @Cacheable(value = "user:menus", key = "#userId")
    public List<MenuVO> getUserMenuTree(Long userId) {
        log.debug("获取用户菜单树，用户ID: {}", userId);
        
        // 查询用户所有菜单权限
        List<SysMenu> userMenus = menuMapper.selectMenusByUserId(userId);
        if (CollectionUtils.isEmpty(userMenus)) {
            return new ArrayList<>();
        }
        
        // 转换为MenuVO并构建树形结构
        List<MenuVO> menuVOList = userMenus.stream()
                .map(this::convertToMenuVO)
                .collect(Collectors.toList());
        
        return buildMenuTree(menuVOList, null);
    }

    @Override
    @Cacheable(value = "user:permissions", key = "#userId")
    public Set<String> getUserPermissions(Long userId) {
        log.debug("获取用户权限标识集合，用户ID: {}", userId);
        
        List<String> permissions = menuMapper.selectPermissionsByUserId(userId);
        return new HashSet<>(permissions);
    }

    @Override
    public boolean hasPermission(Long userId, String permission) {
        if (userId == null || !StringUtils.hasText(permission)) {
            return false;
        }
        
        Set<String> userPermissions = getUserPermissions(userId);
        return userPermissions.contains(permission);
    }

    @Override
    public boolean hasAnyPermission(Long userId, String... permissions) {
        if (userId == null || permissions == null || permissions.length == 0) {
            return false;
        }
        
        Set<String> userPermissions = getUserPermissions(userId);
        return Arrays.stream(permissions)
                .anyMatch(userPermissions::contains);
    }

    @Override
    public boolean hasAllPermissions(Long userId, String... permissions) {
        if (userId == null || permissions == null || permissions.length == 0) {
            return false;
        }
        
        Set<String> userPermissions = getUserPermissions(userId);
        return Arrays.stream(permissions)
                .allMatch(userPermissions::contains);
    }

    @Override
    @Cacheable(value = "user:buttons", key = "#userId")
    public Set<String> getUserButtonPermissions(Long userId) {
        log.debug("获取用户按钮权限，用户ID: {}", userId);
        
        List<SysMenu> buttons = menuMapper.selectButtonsByUserId(userId);
        return buttons.stream()
                .filter(menu -> StringUtils.hasText(menu.getPermission()))
                .map(SysMenu::getPermission)
                .collect(Collectors.toSet());
    }

    @Override
    public boolean hasButtonPermission(Long userId, String buttonPermission) {
        if (userId == null || !StringUtils.hasText(buttonPermission)) {
            return false;
        }
        
        Set<String> buttonPermissions = getUserButtonPermissions(userId);
        return buttonPermissions.contains(buttonPermission);
    }

    @Override
    @Cacheable(value = "user:data:permissions", key = "#userId + ':' + #tableName")
    public DataPermissionContext getUserDataPermissionContext(Long userId, String tableName) {
        log.debug("获取用户数据权限上下文，用户ID: {}, 表名: {}", userId, tableName);
        
        // 查询用户信息
        SysUser user = userMapper.selectById(userId);
        if (user == null) {
            return DataPermissionContext.builder().build();
        }
        
        // 查询用户部门信息
        List<SysDepartment> userDepartments = departmentMapper.selectByUserId(userId);
        
        // 查询用户数据权限规则
        List<SysDataPermission> dataPermissions = dataPermissionMapper.selectByUserIdAndTable(userId, tableName);
        
        return DataPermissionContext.builder()
                .userId(userId)
                .username(user.getUsername())
                .userDepartments(userDepartments)
                .dataPermissions(dataPermissions)
                .build();
    }

    @Override
    public String buildDataPermissionSql(Long userId, String tableName, String originalSql) {
        DataPermissionContext context = getUserDataPermissionContext(userId, tableName);
        
        if (CollectionUtils.isEmpty(context.getDataPermissions())) {
            return originalSql;
        }
        
        StringBuilder sqlBuilder = new StringBuilder(originalSql);
        List<String> conditions = new ArrayList<>();
        
        for (SysDataPermission permission : context.getDataPermissions()) {
            if (StringUtils.hasText(permission.getFilterCondition())) {
                String condition = processFilterCondition(permission.getFilterCondition(), context);
                if (StringUtils.hasText(condition)) {
                    conditions.add(condition);
                }
            }
        }
        
        if (!conditions.isEmpty()) {
            String whereClause = String.join(" AND ", conditions);
            
            // 检查原SQL是否已有WHERE子句
            String upperSql = originalSql.toUpperCase();
            if (upperSql.contains("WHERE")) {
                sqlBuilder.append(" AND (").append(whereClause).append(")");
            } else {
                sqlBuilder.append(" WHERE ").append(whereClause);
            }
        }
        
        return sqlBuilder.toString();
    }

    @Override
    public List<String> getFilteredColumns(Long userId, String tableName) {
        List<SysDataPermission> columnPermissions = dataPermissionMapper.selectByUserTableAndType(
                userId, tableName, "COLUMN");
        
        return columnPermissions.stream()
                .filter(permission -> StringUtils.hasText(permission.getColumnName()))
                .map(SysDataPermission::getColumnName)
                .collect(Collectors.toList());
    }

    @Override
    public void refreshUserPermissionCache(Long userId) {
        log.info("刷新用户权限缓存，用户ID: {}", userId);
        // 这里可以实现具体的缓存刷新逻辑
        // 例如删除Redis中的相关缓存键
    }

    @Override
    public void refreshAllPermissionCache() {
        log.info("刷新所有权限缓存");
        // 这里可以实现具体的缓存刷新逻辑
    }

    /**
     * 将SysMenu转换为MenuVO
     */
    private MenuVO convertToMenuVO(SysMenu menu) {
        MenuVO menuVO = new MenuVO();
        menuVO.setId(menu.getId());
        menuVO.setParentId(menu.getParentId());
        menuVO.setMenuName(menu.getMenuName());
        menuVO.setMenuType(menu.getMenuType());
        menuVO.setPath(menu.getPath());
        menuVO.setComponent(menu.getComponent());
        menuVO.setIcon(menu.getIcon());
        menuVO.setSortOrder(menu.getSortOrder());
        menuVO.setPermission(menu.getPermission());
        menuVO.setStatus(menu.getStatus());
        
        // 设置路由元数据
        MenuVO.Meta meta = new MenuVO.Meta();
        meta.setTitle(menu.getMenuName());
        meta.setIcon(menu.getIcon());
        meta.setHidden(menu.getStatus() == 0);
        meta.setKeepAlive(menu.getKeepAlive() == 1);
        menuVO.setMeta(meta);
        
        return menuVO;
    }

    /**
     * 构建菜单树
     */
    private List<MenuVO> buildMenuTree(List<MenuVO> menuList, Long parentId) {
        return menuList.stream()
                .filter(menu -> Objects.equals(menu.getParentId(), parentId))
                .sorted(Comparator.comparing(MenuVO::getSortOrder))
                .peek(menu -> {
                    List<MenuVO> children = buildMenuTree(menuList, menu.getId());
                    menu.setChildren(children);
                })
                .collect(Collectors.toList());
    }

    /**
     * 处理过滤条件，替换占位符
     */
    private String processFilterCondition(String filterCondition, DataPermissionContext context) {
        if (!StringUtils.hasText(filterCondition)) {
            return "";
        }
        
        String processedCondition = filterCondition;
        
        // 替换用户ID占位符
        processedCondition = processedCondition.replace("${userId}", String.valueOf(context.getUserId()));
        
        // 替换用户名占位符
        if (StringUtils.hasText(context.getUsername())) {
            processedCondition = processedCondition.replace("${username}", "'" + context.getUsername() + "'");
        }
        
        // 替换部门ID占位符
        if (!CollectionUtils.isEmpty(context.getUserDepartments())) {
            String deptIds = context.getUserDepartments().stream()
                    .map(dept -> String.valueOf(dept.getId()))
                    .collect(Collectors.joining(","));
            processedCondition = processedCondition.replace("${deptIds}", deptIds);
        }
        
        return processedCondition;
    }
}