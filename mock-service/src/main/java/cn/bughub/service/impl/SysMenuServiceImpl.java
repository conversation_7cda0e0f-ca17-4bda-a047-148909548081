package cn.bughub.service.impl;

import cn.bughub.entity.SysMenu;
import cn.bughub.mapper.SysMenuMapper;
import cn.bughub.service.PermissionManager;
import cn.bughub.service.SysMenuService;
import cn.bughub.vo.MenuVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 菜单管理服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-11
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysMenuServiceImpl extends ServiceImpl<SysMenuMapper, SysMenu> implements SysMenuService {

    private final SysMenuMapper menuMapper;
    private final PermissionManager permissionManager;

    @Override
    @Cacheable(value = "menu:tree", key = "'all'")
    public List<MenuVO> getMenuTree() {
        log.debug("获取完整菜单树");
        
        List<SysMenu> allMenus = menuMapper.selectList(
                new LambdaQueryWrapper<SysMenu>()
                        .eq(SysMenu::getDeleted, 0)
                        .orderByAsc(SysMenu::getSortOrder)
        );
        
        if (CollectionUtils.isEmpty(allMenus)) {
            return new ArrayList<>();
        }
        
        List<MenuVO> menuVOList = allMenus.stream()
                .map(this::convertToMenuVO)
                .collect(Collectors.toList());
        
        return buildMenuTree(menuVOList, null);
    }

    @Override
    public List<MenuVO> getUserMenuTree(Long userId) {
        return permissionManager.getUserMenuTree(userId);
    }

    @Override
    public List<SysMenu> getMenusByParentId(Long parentId) {
        return menuMapper.selectByParentId(parentId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"menu:tree", "user:menus"}, allEntries = true)
    public boolean createMenu(SysMenu menu) {
        log.info("创建菜单: {}", menu.getMenuName());
        
        // 设置创建时间
        menu.setCreateTime(LocalDateTime.now());
        menu.setUpdateTime(LocalDateTime.now());
        menu.setDeleted(0);
        
        // 如果是根菜单，设置父ID为null
        if (menu.getParentId() != null && menu.getParentId() == 0) {
            menu.setParentId(null);
        }
        
        // 验证权限标识唯一性
        if (StringUtils.hasText(menu.getPermission())) {
            SysMenu existingMenu = menuMapper.selectByPermission(menu.getPermission());
            if (existingMenu != null) {
                throw new RuntimeException("权限标识已存在: " + menu.getPermission());
            }
        }
        
        // 验证路由路径唯一性
        if (StringUtils.hasText(menu.getPath())) {
            SysMenu existingMenu = menuMapper.selectByPath(menu.getPath());
            if (existingMenu != null) {
                throw new RuntimeException("路由路径已存在: " + menu.getPath());
            }
        }
        
        return save(menu);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"menu:tree", "user:menus"}, allEntries = true)
    public boolean updateMenu(SysMenu menu) {
        log.info("更新菜单: {}", menu.getMenuName());
        
        // 设置更新时间
        menu.setUpdateTime(LocalDateTime.now());
        
        // 验证权限标识唯一性（排除自己）
        if (StringUtils.hasText(menu.getPermission())) {
            SysMenu existingMenu = menuMapper.selectByPermission(menu.getPermission());
            if (existingMenu != null && !existingMenu.getId().equals(menu.getId())) {
                throw new RuntimeException("权限标识已存在: " + menu.getPermission());
            }
        }
        
        // 验证路由路径唯一性（排除自己）
        if (StringUtils.hasText(menu.getPath())) {
            SysMenu existingMenu = menuMapper.selectByPath(menu.getPath());
            if (existingMenu != null && !existingMenu.getId().equals(menu.getId())) {
                throw new RuntimeException("路由路径已存在: " + menu.getPath());
            }
        }
        
        return updateById(menu);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"menu:tree", "user:menus"}, allEntries = true)
    public boolean deleteMenu(Long menuId) {
        log.info("删除菜单，ID: {}", menuId);
        
        // 检查是否有子菜单
        if (hasChildren(menuId)) {
            throw new RuntimeException("存在子菜单，无法删除");
        }
        
        // 软删除
        SysMenu menu = new SysMenu();
        menu.setId(menuId);
        menu.setDeleted(1);
        menu.setUpdateTime(LocalDateTime.now());
        
        return updateById(menu);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"menu:tree", "user:menus"}, allEntries = true)
    public boolean batchDeleteMenus(List<Long> menuIds) {
        log.info("批量删除菜单，IDs: {}", menuIds);
        
        if (CollectionUtils.isEmpty(menuIds)) {
            return true;
        }
        
        // 检查每个菜单是否有子菜单
        for (Long menuId : menuIds) {
            if (hasChildren(menuId)) {
                SysMenu menu = getById(menuId);
                throw new RuntimeException("菜单 [" + menu.getMenuName() + "] 存在子菜单，无法删除");
            }
        }
        
        // 批量软删除
        List<SysMenu> menusToUpdate = menuIds.stream()
                .map(id -> {
                    SysMenu menu = new SysMenu();
                    menu.setId(id);
                    menu.setDeleted(1);
                    menu.setUpdateTime(LocalDateTime.now());
                    return menu;
                })
                .collect(Collectors.toList());
        
        return updateBatchById(menusToUpdate);
    }

    @Override
    public boolean hasChildren(Long menuId) {
        return menuMapper.countChildren(menuId) > 0;
    }

    @Override
    public SysMenu getMenuByPermission(String permission) {
        return menuMapper.selectByPermission(permission);
    }

    @Override
    public SysMenu getMenuByPath(String path) {
        return menuMapper.selectByPath(path);
    }

    @Override
    @Cacheable(value = "menu:enabled", key = "'all'")
    public List<SysMenu> getEnabledMenus() {
        return menuMapper.selectEnabledMenus();
    }

    @Override
    public List<SysMenu> getMenusByType(Integer menuType) {
        return menuMapper.selectByMenuType(menuType);
    }

    @Override
    @CacheEvict(value = {"menu:tree", "user:menus", "menu:enabled"}, allEntries = true)
    public boolean updateMenuStatus(Long menuId, Integer status) {
        log.info("更新菜单状态，ID: {}, 状态: {}", menuId, status);
        
        SysMenu menu = new SysMenu();
        menu.setId(menuId);
        menu.setStatus(status);
        menu.setUpdateTime(LocalDateTime.now());
        
        return updateById(menu);
    }

    /**
     * 将SysMenu转换为MenuVO
     */
    private MenuVO convertToMenuVO(SysMenu menu) {
        MenuVO menuVO = new MenuVO();
        menuVO.setId(menu.getId());
        menuVO.setParentId(menu.getParentId());
        menuVO.setMenuName(menu.getMenuName());
        menuVO.setMenuType(menu.getMenuType());
        menuVO.setPath(menu.getPath());
        menuVO.setComponent(menu.getComponent());
        menuVO.setIcon(menu.getIcon());
        menuVO.setSortOrder(menu.getSortOrder());
        menuVO.setPermission(menu.getPermission());
        menuVO.setStatus(menu.getStatus());
        
        // 设置路由元数据
        MenuVO.Meta meta = new MenuVO.Meta();
        meta.setTitle(menu.getMenuName());
        meta.setIcon(menu.getIcon());
        meta.setHidden(menu.getStatus() == 0);
        meta.setKeepAlive(menu.getKeepAlive() == 1);
        menuVO.setMeta(meta);
        
        return menuVO;
    }

    /**
     * 构建菜单树
     */
    private List<MenuVO> buildMenuTree(List<MenuVO> menuList, Long parentId) {
        return menuList.stream()
                .filter(menu -> Objects.equals(menu.getParentId(), parentId))
                .sorted(Comparator.comparing(MenuVO::getSortOrder))
                .peek(menu -> {
                    List<MenuVO> children = buildMenuTree(menuList, menu.getId());
                    menu.setChildren(children);
                })
                .collect(Collectors.toList());
    }
}