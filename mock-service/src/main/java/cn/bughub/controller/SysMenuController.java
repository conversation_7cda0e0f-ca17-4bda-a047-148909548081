package cn.bughub.controller;

import cn.bughub.annotation.RequiresPermission;
import cn.bughub.common.response.Result;
import cn.bughub.entity.SysMenu;
import cn.bughub.service.SysMenuService;
import cn.bughub.vo.MenuVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 菜单管理控制器
 * 
 * <AUTHOR>
 * @since 2025-01-11
 */
@Slf4j
@RestController
@RequestMapping("/api/system/menu")
@RequiredArgsConstructor
@Validated
@Tag(name = "菜单管理", description = "菜单管理相关接口")
public class SysMenuController {

    private final SysMenuService menuService;

    @GetMapping("/tree")
    @Operation(summary = "获取菜单树", description = "获取完整的菜单树结构")
    @RequiresPermission("system:menu:view")
    public Result<List<MenuVO>> getMenuTree() {
        List<MenuVO> menuTree = menuService.getMenuTree();
        return Result.success(menuTree);
    }

    @GetMapping("/user/{userId}")
    @Operation(summary = "获取用户菜单树", description = "根据用户ID获取用户有权限的菜单树")
    @RequiresPermission("system:menu:view")
    public Result<List<MenuVO>> getUserMenuTree(
            @Parameter(description = "用户ID") @PathVariable @NotNull Long userId) {
        List<MenuVO> userMenuTree = menuService.getUserMenuTree(userId);
        return Result.success(userMenuTree);
    }

    @GetMapping("/parent/{parentId}")
    @Operation(summary = "获取子菜单列表", description = "根据父菜单ID获取子菜单列表")
    @RequiresPermission("system:menu:view")
    public Result<List<SysMenu>> getMenusByParentId(
            @Parameter(description = "父菜单ID") @PathVariable Long parentId) {
        List<SysMenu> menus = menuService.getMenusByParentId(parentId);
        return Result.success(menus);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取菜单详情", description = "根据菜单ID获取菜单详细信息")
    @RequiresPermission("system:menu:view")
    public Result<SysMenu> getMenuById(
            @Parameter(description = "菜单ID") @PathVariable @NotNull Long id) {
        SysMenu menu = menuService.getById(id);
        if (menu == null) {
            return Result.error("菜单不存在");
        }
        return Result.success(menu);
    }

    @PostMapping
    @Operation(summary = "创建菜单", description = "创建新的菜单")
    @RequiresPermission("system:menu:add")
    public Result<Void> createMenu(@Valid @RequestBody SysMenu menu) {
        boolean success = menuService.createMenu(menu);
        if (success) {
            return Result.success("菜单创建成功");
        } else {
            return Result.error("菜单创建失败");
        }
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新菜单", description = "更新菜单信息")
    @RequiresPermission("system:menu:edit")
    public Result<Void> updateMenu(
            @Parameter(description = "菜单ID") @PathVariable @NotNull Long id,
            @Valid @RequestBody SysMenu menu) {
        menu.setId(id);
        boolean success = menuService.updateMenu(menu);
        if (success) {
            return Result.success("菜单更新成功");
        } else {
            return Result.error("菜单更新失败");
        }
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除菜单", description = "根据菜单ID删除菜单")
    @RequiresPermission("system:menu:delete")
    public Result<Void> deleteMenu(
            @Parameter(description = "菜单ID") @PathVariable @NotNull Long id) {
        try {
            boolean success = menuService.deleteMenu(id);
            if (success) {
                return Result.success("菜单删除成功");
            } else {
                return Result.error("菜单删除失败");
            }
        } catch (RuntimeException e) {
            return Result.error(e.getMessage());
        }
    }

    @DeleteMapping("/batch")
    @Operation(summary = "批量删除菜单", description = "批量删除多个菜单")
    @RequiresPermission("system:menu:delete")
    public Result<Void> batchDeleteMenus(
            @Parameter(description = "菜单ID列表") @RequestBody @NotEmpty List<Long> menuIds) {
        try {
            boolean success = menuService.batchDeleteMenus(menuIds);
            if (success) {
                return Result.success("菜单批量删除成功");
            } else {
                return Result.error("菜单批量删除失败");
            }
        } catch (RuntimeException e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/type/{menuType}")
    @Operation(summary = "根据类型获取菜单", description = "根据菜单类型获取菜单列表")
    @RequiresPermission("system:menu:view")
    public Result<List<SysMenu>> getMenusByType(
            @Parameter(description = "菜单类型") @PathVariable @NotNull Integer menuType) {
        List<SysMenu> menus = menuService.getMenusByType(menuType);
        return Result.success(menus);
    }

    @GetMapping("/enabled")
    @Operation(summary = "获取启用菜单", description = "获取所有启用状态的菜单")
    @RequiresPermission("system:menu:view")
    public Result<List<SysMenu>> getEnabledMenus() {
        List<SysMenu> menus = menuService.getEnabledMenus();
        return Result.success(menus);
    }

    @PutMapping("/{id}/status")
    @Operation(summary = "更新菜单状态", description = "更新菜单的启用/禁用状态")
    @RequiresPermission("system:menu:edit")
    public Result<Void> updateMenuStatus(
            @Parameter(description = "菜单ID") @PathVariable @NotNull Long id,
            @Parameter(description = "状态") @RequestParam @NotNull Integer status) {
        boolean success = menuService.updateMenuStatus(id, status);
        if (success) {
            return Result.success("菜单状态更新成功");
        } else {
            return Result.error("菜单状态更新失败");
        }
    }

    @GetMapping("/permission/{permission}")
    @Operation(summary = "根据权限标识获取菜单", description = "根据权限标识获取菜单信息")
    @RequiresPermission("system:menu:view")
    public Result<SysMenu> getMenuByPermission(
            @Parameter(description = "权限标识") @PathVariable String permission) {
        SysMenu menu = menuService.getMenuByPermission(permission);
        if (menu == null) {
            return Result.error("菜单不存在");
        }
        return Result.success(menu);
    }

    @GetMapping("/path")
    @Operation(summary = "根据路径获取菜单", description = "根据路由路径获取菜单信息")
    @RequiresPermission("system:menu:view")
    public Result<SysMenu> getMenuByPath(
            @Parameter(description = "路由路径") @RequestParam String path) {
        SysMenu menu = menuService.getMenuByPath(path);
        if (menu == null) {
            return Result.error("菜单不存在");
        }
        return Result.success(menu);
    }

    @GetMapping("/{id}/children/exists")
    @Operation(summary = "检查是否有子菜单", description = "检查指定菜单是否有子菜单")
    @RequiresPermission("system:menu:view")
    public Result<Boolean> hasChildren(
            @Parameter(description = "菜单ID") @PathVariable @NotNull Long id) {
        boolean hasChildren = menuService.hasChildren(id);
        return Result.success(hasChildren);
    }
}