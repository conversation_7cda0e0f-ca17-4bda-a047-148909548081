package cn.bughub.annotation;

import java.lang.annotation.*;

/**
 * 权限验证注解
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequiresPermission {

    /**
     * 权限标识数组
     */
    String[] value() default {};

    /**
     * 逻辑关系：AND-所有权限都必须有，OR-有其中一个权限即可
     */
    Logical logical() default Logical.AND;

    /**
     * 逻辑关系枚举
     */
    enum Logical {
        /** 并且 */
        AND,
        /** 或者 */
        OR
    }
}