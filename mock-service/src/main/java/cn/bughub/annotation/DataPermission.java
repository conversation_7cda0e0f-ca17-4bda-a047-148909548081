package cn.bughub.annotation;

import java.lang.annotation.*;

/**
 * 数据权限注解
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DataPermission {

    /**
     * 表名数组，为空时表示对所有表生效
     */
    String[] tables() default {};

    /**
     * 列名数组，为空时表示对所有列生效
     */
    String[] columns() default {};

    /**
     * 自定义过滤条件
     */
    String condition() default "";

    /**
     * 数据权限类型
     */
    Type type() default Type.AUTO;

    /**
     * 是否启用数据权限
     */
    boolean enabled() default true;

    /**
     * 数据权限类型枚举
     */
    enum Type {
        /** 自动根据用户数据权限范围处理 */
        AUTO,
        /** 全部数据 */
        ALL,
        /** 本部门及下级部门数据 */
        DEPT_AND_CHILD,
        /** 本部门数据 */
        DEPT,
        /** 仅本人数据 */
        SELF,
        /** 自定义数据权限 */
        CUSTOM
    }
}