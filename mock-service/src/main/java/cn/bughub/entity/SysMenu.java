package cn.bughub.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 菜单权限实体类
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_menu")
public class SysMenu {

    /**
     * 菜单ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 父菜单ID
     */
    private Long parentId;

    /**
     * 菜单名称
     */
    private String menuName;

    /**
     * 菜单编码
     */
    private String menuCode;

    /**
     * 菜单类型：1-目录，2-菜单，3-按钮
     */
    private Integer menuType;

    /**
     * 路由路径
     */
    private String path;

    /**
     * 组件路径
     */
    private String component;

    /**
     * 菜单图标
     */
    private String icon;

    /**
     * 显示顺序
     */
    private Integer sortOrder;

    /**
     * 是否显示：0-隐藏，1-显示
     */
    private Integer visible;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 权限标识
     */
    private String perms;

    /**
     * 路由参数
     */
    private String queryParams;

    /**
     * 是否外链：0-否，1-是
     */
    private Integer isFrame;

    /**
     * 是否缓存：0-不缓存，1-缓存
     */
    private Integer isCache;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 子菜单列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<SysMenu> children;

    /**
     * 父菜单名称（非数据库字段）
     */
    @TableField(exist = false)
    private String parentName;

    /**
     * 菜单层级（非数据库字段）
     */
    @TableField(exist = false)
    private Integer level;

    /**
     * 菜单路径（非数据库字段）
     */
    @TableField(exist = false)
    private String menuPath;

    /**
     * 菜单类型常量
     */
    public static class MenuType {
        /** 目录 */
        public static final int CATALOG = 1;
        /** 菜单 */
        public static final int MENU = 2;
        /** 按钮 */
        public static final int BUTTON = 3;
    }

    /**
     * 状态常量
     */
    public static class Status {
        /** 禁用 */
        public static final int DISABLED = 0;
        /** 启用 */
        public static final int ENABLED = 1;
    }

    /**
     * 显示状态常量
     */
    public static class Visible {
        /** 隐藏 */
        public static final int HIDDEN = 0;
        /** 显示 */
        public static final int SHOW = 1;
    }

    /**
     * 外链常量
     */
    public static class Frame {
        /** 否 */
        public static final int NO = 0;
        /** 是 */
        public static final int YES = 1;
    }

    /**
     * 缓存常量
     */
    public static class Cache {
        /** 不缓存 */
        public static final int NO = 0;
        /** 缓存 */
        public static final int YES = 1;
    }
}