package cn.bughub.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 数据权限规则实体类
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_data_permission")
public class SysDataPermission {

    /**
     * 数据权限ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则编码
     */
    private String ruleCode;

    /**
     * 资源类型：table-表，column-列
     */
    private String resourceType;

    /**
     * 资源名称
     */
    private String resourceName;

    /**
     * 权限类型：1-查看，2-编辑，3-删除
     */
    private Integer permissionType;

    /**
     * 过滤条件SQL
     */
    private String filterCondition;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 资源类型常量
     */
    public static class ResourceType {
        /** 表 */
        public static final String TABLE = "table";
        /** 列 */
        public static final String COLUMN = "column";
    }

    /**
     * 权限类型常量
     */
    public static class PermissionType {
        /** 查看 */
        public static final int VIEW = 1;
        /** 编辑 */
        public static final int EDIT = 2;
        /** 删除 */
        public static final int DELETE = 3;
    }

    /**
     * 状态常量
     */
    public static class Status {
        /** 禁用 */
        public static final int DISABLED = 0;
        /** 启用 */
        public static final int ENABLED = 1;
    }
}