package cn.bughub.interceptor;

import cn.bughub.dto.DataPermissionContext;
import cn.bughub.security.UserPrincipal;
import cn.bughub.service.PermissionManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.Select;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 数据权限拦截器
 * 自动为SQL添加数据权限过滤条件（非注解方式）
 * 
 * <AUTHOR>
 * @since 2025-01-11
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Intercepts({
    @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class})
})
public class DataPermissionInterceptor implements Interceptor {

    private final PermissionManager permissionManager;
    
    // 需要进行数据权限过滤的表名模式
    private static final Pattern TABLE_PATTERN = Pattern.compile("FROM\\s+(\\w+)", Pattern.CASE_INSENSITIVE);
    
    // 排除的Mapper方法（不进行数据权限过滤）
    private static final String[] EXCLUDED_MAPPERS = {
        "cn.bughub.mapper.SysUserMapper.selectById",
        "cn.bughub.mapper.SysRoleMapper",
        "cn.bughub.mapper.SysPermissionMapper",
        "cn.bughub.mapper.SysMenuMapper.selectPermissionsByUserId",
        "cn.bughub.mapper.SysDataPermissionMapper"
    };

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        Object[] args = invocation.getArgs();
        MappedStatement mappedStatement = (MappedStatement) args[0];
        Object parameter = args[1];
        RowBounds rowBounds = (RowBounds) args[2];
        ResultHandler resultHandler = (ResultHandler) args[3];
        
        // 获取当前用户信息
        Long currentUserId = getCurrentUserId();
        if (currentUserId == null) {
            // 未登录用户，直接执行原SQL
            return invocation.proceed();
        }
        
        // 检查是否需要排除数据权限过滤
        String mapperId = mappedStatement.getId();
        if (shouldExclude(mapperId)) {
            return invocation.proceed();
        }
        
        // 获取原始SQL
        BoundSql boundSql = mappedStatement.getBoundSql(parameter);
        String originalSql = boundSql.getSql();
        
        // 提取表名
        String tableName = extractTableName(originalSql);
        if (!StringUtils.hasText(tableName)) {
            return invocation.proceed();
        }
        
        // 构建数据权限过滤SQL
        String filteredSql = buildDataPermissionSql(currentUserId, tableName, originalSql);
        
        if (!originalSql.equals(filteredSql)) {
            log.debug("应用数据权限过滤，用户ID: {}, 表名: {}", currentUserId, tableName);
            log.debug("原始SQL: {}", originalSql);
            log.debug("过滤后SQL: {}", filteredSql);
            
            // 创建新的BoundSql
            BoundSql newBoundSql = new BoundSql(mappedStatement.getConfiguration(), 
                    filteredSql, boundSql.getParameterMappings(), parameter);
            
            // 复制额外参数
            for (String key : boundSql.getAdditionalParameterNames()) {
                newBoundSql.setAdditionalParameter(key, boundSql.getAdditionalParameter(key));
            }
            
            // 创建新的MappedStatement
            MappedStatement newMappedStatement = copyMappedStatement(mappedStatement, newBoundSql);
            args[0] = newMappedStatement;
        }
        
        return invocation.proceed();
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        // 可以通过配置文件设置属性
    }

    /**
     * 获取当前登录用户ID
     */
    private Long getCurrentUserId() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.getPrincipal() instanceof UserPrincipal) {
                UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
                return userPrincipal.getId();
            }
        } catch (Exception e) {
            log.warn("获取当前用户信息失败", e);
        }
        return null;
    }

    /**
     * 检查是否应该排除数据权限过滤
     */
    private boolean shouldExclude(String mapperId) {
        for (String excluded : EXCLUDED_MAPPERS) {
            if (mapperId.startsWith(excluded)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 从SQL中提取表名
     */
    private String extractTableName(String sql) {
        try {
            Statement statement = CCJSqlParserUtil.parse(sql);
            if (statement instanceof Select) {
                // 这里可以使用更复杂的SQL解析逻辑
                // 为简化实现，使用正则表达式
                Matcher matcher = TABLE_PATTERN.matcher(sql);
                if (matcher.find()) {
                    return matcher.group(1);
                }
            }
        } catch (JSQLParserException e) {
            log.warn("SQL解析失败: {}", sql, e);
            // 降级使用正则表达式
            Matcher matcher = TABLE_PATTERN.matcher(sql);
            if (matcher.find()) {
                return matcher.group(1);
            }
        }
        return null;
    }

    /**
     * 构建数据权限过滤SQL
     */
    private String buildDataPermissionSql(Long userId, String tableName, String originalSql) {
        try {
            return permissionManager.buildDataPermissionSql(userId, tableName, originalSql);
        } catch (Exception e) {
            log.error("构建数据权限SQL失败，用户ID: {}, 表名: {}", userId, tableName, e);
            return originalSql;
        }
    }

    /**
     * 复制MappedStatement
     */
    private MappedStatement copyMappedStatement(MappedStatement mappedStatement, BoundSql newBoundSql) {
        MappedStatement.Builder builder = new MappedStatement.Builder(
                mappedStatement.getConfiguration(),
                mappedStatement.getId(),
                new BoundSqlSqlSource(newBoundSql),
                mappedStatement.getSqlCommandType()
        );
        
        builder.resource(mappedStatement.getResource());
        builder.fetchSize(mappedStatement.getFetchSize());
        builder.statementType(mappedStatement.getStatementType());
        builder.keyGenerator(mappedStatement.getKeyGenerator());
        if (mappedStatement.getKeyProperties() != null) {
            builder.keyProperty(String.join(",", mappedStatement.getKeyProperties()));
        }
        if (mappedStatement.getKeyColumns() != null) {
            builder.keyColumn(String.join(",", mappedStatement.getKeyColumns()));
        }
        builder.databaseId(mappedStatement.getDatabaseId());
        builder.lang(mappedStatement.getLang());
        if (mappedStatement.getResultSets() != null) {
            builder.resultSets(String.join(",", mappedStatement.getResultSets()));
        }
        builder.resultMaps(mappedStatement.getResultMaps());
        builder.resultSetType(mappedStatement.getResultSetType());
        builder.flushCacheRequired(mappedStatement.isFlushCacheRequired());
        builder.useCache(mappedStatement.isUseCache());
        builder.cache(mappedStatement.getCache());
        builder.parameterMap(mappedStatement.getParameterMap());
        builder.timeout(mappedStatement.getTimeout());
        
        return builder.build();
    }

    /**
     * BoundSql包装类
     */
    private static class BoundSqlSqlSource implements org.apache.ibatis.mapping.SqlSource {
        private final BoundSql boundSql;

        public BoundSqlSqlSource(BoundSql boundSql) {
            this.boundSql = boundSql;
        }

        @Override
        public BoundSql getBoundSql(Object parameterObject) {
            return boundSql;
        }
    }
}