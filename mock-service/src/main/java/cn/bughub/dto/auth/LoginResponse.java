package cn.bughub.dto.auth;

import cn.bughub.entity.SysPermission;
import cn.bughub.entity.SysRole;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 登录响应DTO
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LoginResponse {

    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * 刷新令牌
     */
    private String refreshToken;

    /**
     * 令牌类型
     */
    private String tokenType = "Bearer";

    /**
     * 过期时间（秒）
     */
    private Long expiresIn;

    /**
     * 用户信息
     */
    private UserInfo userInfo;

    /**
     * 用户信息内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserInfo {
        /**
         * 用户ID
         */
        private Long id;

        /**
         * 用户名
         */
        private String username;

        /**
         * 昵称
         */
        private String nickname;

        /**
         * 邮箱
         */
        private String email;

        /**
         * 手机号
         */
        private String mobile;

        /**
         * 头像
         */
        private String avatar;

        /**
         * 角色列表
         */
        private List<RoleInfo> roles;

        /**
         * 权限列表
         */
        private List<String> permissions;

        /**
         * 菜单列表
         */
        private List<MenuInfo> menus;
    }

    /**
     * 角色信息内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RoleInfo {
        private Long id;
        private String roleName;
        private String roleCode;
        private String description;
    }

    /**
     * 菜单信息内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MenuInfo {
        private Long id;
        private Long parentId;
        private String name;
        private String code;
        private String path;
        private String icon;
        private Integer sort;
        private List<MenuInfo> children;
    }
}