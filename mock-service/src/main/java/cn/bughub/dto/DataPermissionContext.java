package cn.bughub.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 数据权限上下文
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
public class DataPermissionContext {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 部门ID
     */
    private Long departmentId;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 数据权限范围：1-全部，2-本部门及下级，3-本部门，4-仅本人，5-自定义
     */
    private Integer dataScope;

    /**
     * 角色ID列表
     */
    private List<Long> roleIds;

    /**
     * 角色编码列表
     */
    private List<String> roleCodes;

    /**
     * 部门ID列表（包含子部门）
     */
    private List<Long> departmentIds;

    /**
     * 表级权限规则
     * Key: 表名, Value: 过滤条件SQL
     */
    private Map<String, String> tablePermissions;

    /**
     * 列级权限规则
     * Key: 表名, Value: 允许访问的列名集合
     */
    private Map<String, Set<String>> columnPermissions;

    /**
     * 自定义数据权限规则
     */
    private List<DataPermissionRule> customRules;

    /**
     * 权限缓存时间戳
     */
    private Long cacheTimestamp;

    /**
     * 数据权限范围常量
     */
    public static class DataScope {
        /** 全部数据权限 */
        public static final int ALL = 1;
        /** 本部门及下级数据权限 */
        public static final int DEPT_AND_CHILD = 2;
        /** 本部门数据权限 */
        public static final int DEPT = 3;
        /** 仅本人数据权限 */
        public static final int SELF = 4;
        /** 自定义数据权限 */
        public static final int CUSTOM = 5;
    }

    /**
     * 数据权限规则内部类
     */
    @Data
    public static class DataPermissionRule {
        /**
         * 规则ID
         */
        private Long ruleId;

        /**
         * 规则名称
         */
        private String ruleName;

        /**
         * 规则编码
         */
        private String ruleCode;

        /**
         * 资源类型
         */
        private String resourceType;

        /**
         * 资源名称
         */
        private String resourceName;

        /**
         * 权限类型
         */
        private Integer permissionType;

        /**
         * 过滤条件
         */
        private String filterCondition;

        /**
         * 是否启用
         */
        private Boolean enabled;
    }

    /**
     * 检查是否有表权限
     * 
     * @param tableName 表名
     * @return 是否有权限
     */
    public boolean hasTablePermission(String tableName) {
        return tablePermissions != null && tablePermissions.containsKey(tableName);
    }

    /**
     * 获取表权限过滤条件
     * 
     * @param tableName 表名
     * @return 过滤条件SQL
     */
    public String getTableFilterCondition(String tableName) {
        return tablePermissions != null ? tablePermissions.get(tableName) : null;
    }

    /**
     * 检查是否有列权限
     * 
     * @param tableName 表名
     * @param columnName 列名
     * @return 是否有权限
     */
    public boolean hasColumnPermission(String tableName, String columnName) {
        if (columnPermissions == null) {
            return true; // 默认有权限
        }
        Set<String> allowedColumns = columnPermissions.get(tableName);
        return allowedColumns == null || allowedColumns.contains(columnName);
    }

    /**
     * 获取表的允许访问列
     * 
     * @param tableName 表名
     * @return 允许访问的列名集合
     */
    public Set<String> getAllowedColumns(String tableName) {
        return columnPermissions != null ? columnPermissions.get(tableName) : null;
    }

    /**
     * 是否为超级管理员（拥有全部数据权限）
     * 
     * @return 是否为超级管理员
     */
    public boolean isSuperAdmin() {
        return DataScope.ALL == dataScope;
    }

    /**
     * 是否为部门管理员（拥有本部门及下级数据权限）
     * 
     * @return 是否为部门管理员
     */
    public boolean isDeptAdmin() {
        return DataScope.DEPT_AND_CHILD == dataScope;
    }

    /**
     * 是否只能查看本人数据
     * 
     * @return 是否只能查看本人数据
     */
    public boolean isSelfOnly() {
        return DataScope.SELF == dataScope;
    }
}