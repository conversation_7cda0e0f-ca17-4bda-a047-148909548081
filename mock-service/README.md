# Mock Server - 服务端工具类使用说明

## 项目结构

```
mock-server/
├── src/main/java/cn/bughub/
│   ├── common/
│   │   ├── response/
│   │   │   ├── Result.java          # 统一响应实体
│   │   │   ├── ResultCode.java      # 响应码枚举
│   │   │   └── PageResult.java      # 分页响应实体
│   │   └── exception/
│   │       ├── BusinessException.java       # 业务异常类
│   │       └── GlobalExceptionHandler.java  # 全局异常处理器
│   ├── controller/
│   │   └── TestController.java      # 测试控制器
│   └── MockServerApp.java           # 启动类
```

## 核心功能

### 1. 统一响应实体 (Result.java)

统一的API响应格式，包含以下字段：
- `code`: 响应码
- `message`: 响应消息
- `data`: 响应数据
- `timestamp`: 时间戳
- `requestId`: 请求ID（用于链路追踪）

**使用示例：**

```java
// 成功响应（无数据）
Result.success();

// 成功响应（带数据）
Result.success(data);

// 成功响应（自定义消息）
Result.success("操作成功");

// 失败响应
Result.failed();

// 失败响应（自定义消息）
Result.failed("操作失败");

// 失败响应（使用ResultCode枚举）
Result.failed(ResultCode.USER_NOT_EXIST);

// 参数验证失败
Result.validateFailed("参数错误");

// 未认证
Result.unauthorized();

// 未授权
Result.forbidden();
```

### 2. 响应码枚举 (ResultCode.java)

定义了系统中所有的响应码，包括：
- 通用响应码（200-599）
- 用户相关错误码（10000-19999）
- Token相关错误码（20000-29999）
- 验证码相关错误码（30000-39999）
- 项目相关错误码（40000-49999）
- Mock接口相关错误码（50000-59999）
- Mock规则相关错误码（60000-69999）
- 文件相关错误码（70000-79999）
- 数据相关错误码（80000-89999）

### 3. 分页响应实体 (PageResult.java)

用于封装分页查询结果，包含：
- `list`: 数据列表
- `total`: 总记录数
- `pageNum`: 当前页码
- `pageSize`: 每页数量
- `pages`: 总页数
- `hasNext`: 是否有下一页
- `hasPrevious`: 是否有上一页

**使用示例：**

```java
List<User> userList = userService.queryUsers(pageNum, pageSize);
Long total = userService.countUsers();
PageResult<User> pageResult = PageResult.of(userList, total, pageNum, pageSize);
return Result.success(pageResult);
```

### 4. 业务异常类 (BusinessException.java)

自定义业务异常，支持多种构造方式：

```java
// 使用错误消息
throw new BusinessException("用户不存在");

// 使用错误码和消息
throw new BusinessException(10001, "用户不存在");

// 使用ResultCode枚举
throw new BusinessException(ResultCode.USER_NOT_EXIST);

// 带错误数据
throw new BusinessException(ResultCode.USER_ALREADY_EXIST, errorData);
```

### 5. 全局异常处理器 (GlobalExceptionHandler.java)

自动处理各种异常，返回统一的错误响应：
- 业务异常 (BusinessException)
- 参数验证异常 (MethodArgumentNotValidException, BindException, ConstraintViolationException)
- 缺少请求参数异常 (MissingServletRequestParameterException)
- 参数类型不匹配异常 (MethodArgumentTypeMismatchException)
- HTTP消息不可读异常 (HttpMessageNotReadableException)
- 不支持的HTTP方法异常 (HttpRequestMethodNotSupportedException)
- 404异常 (NoHandlerFoundException)
- 空指针异常 (NullPointerException)
- 其他运行时异常和未知异常

## 测试接口

启动应用后，可以访问以下测试接口：

### 成功响应测试
```bash
# 带数据的成功响应
GET http://localhost:8080/api/test/success

# 无数据的成功响应
GET http://localhost:8080/api/test/success-no-data

# 分页响应
GET http://localhost:8080/api/test/page?pageNum=1&pageSize=10
```

### 异常处理测试
```bash
# 业务异常
GET http://localhost:8080/api/test/business-error

# 自定义错误
GET http://localhost:8080/api/test/custom-error

# 带数据的错误
GET http://localhost:8080/api/test/error-with-data

# 参数验证（Query参数）
GET http://localhost:8080/api/test/validate-query?name=&age=

# 参数验证（RequestBody）
POST http://localhost:8080/api/test/validate-body
Content-Type: application/json
{
    "username": "",
    "password": "",
    "age": null
}

# 空指针异常
GET http://localhost:8080/api/test/null-pointer

# 运行时异常
GET http://localhost:8080/api/test/runtime-error

# 404错误
GET http://localhost:8080/api/test/not-found
```

## 配置说明

### application.yml 配置示例

```yaml
server:
  port: 8080
  servlet:
    context-path: /

spring:
  application:
    name: mock-server
  
  # 数据库配置
  datasource:
    url: ***************************************************************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver
  
  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath:/mapper/**/*.xml
  global-config:
    db-config:
      id-type: auto
      logic-delete-value: 1
      logic-not-delete-value: 0
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 日志配置
logging:
  level:
    cn.bughub: debug
    org.springframework.web: debug
```

## 运行项目

1. 确保已安装 JDK 17+
2. 确保已安装 Maven 3.6+
3. 配置数据库连接（可选）
4. 运行项目：

```bash
# 编译项目
mvn clean compile

# 运行项目
mvn spring-boot:run

# 或者直接运行主类
java -jar target/mock-server-1.0-SNAPSHOT.jar
```

## 响应示例

### 成功响应
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "id": 1,
        "name": "测试数据",
        "time": "2025-08-08 11:30:00"
    },
    "timestamp": 1723089000000
}
```

### 分页响应
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "list": [
            {"id": 1, "name": "数据1"},
            {"id": 2, "name": "数据2"}
        ],
        "total": 100,
        "pageNum": 1,
        "pageSize": 10,
        "pages": 10,
        "hasNext": true,
        "hasPrevious": false
    },
    "timestamp": 1723089000000
}
```

### 错误响应
```json
{
    "code": 10001,
    "message": "用户不存在",
    "timestamp": 1723089000000
}
```

### 参数验证错误响应
```json
{
    "code": 400,
    "message": "username: 用户名不能为空, password: 密码不能为空",
    "data": {
        "username": "用户名不能为空",
        "password": "密码不能为空"
    },
    "timestamp": 1723089000000
}
```

## 最佳实践

1. **统一使用Result返回**：所有Controller方法都应返回`Result<T>`类型
2. **合理使用异常**：业务错误使用`BusinessException`，系统错误让全局异常处理器处理
3. **定义清晰的错误码**：在`ResultCode`中定义所有业务错误码，便于前端处理
4. **参数验证**：使用Jakarta Validation注解进行参数验证
5. **分页查询**：使用`PageResult`封装分页数据
6. **日志记录**：在异常处理器中记录详细的错误日志

## 扩展建议

1. 添加请求ID生成和传递机制，用于链路追踪
2. 添加接口访问日志记录
3. 添加接口限流功能
4. 添加数据脱敏功能
5. 添加国际化支持
6. 集成Swagger/OpenAPI文档

---

作者：zhengwenj  
日期：2025/08/08