# Maven
target/
!.mvn/wrapper/maven-wrapper.jar
!**/src/main/**/target/
!**/src/test/**/target/

# IntelliJ IDEA
.idea
*.iws
*.iml
*.ipr
out/
!**/src/main/**/out/
!**/src/test/**/out/

# External tool builders
.externalToolBuilders/

# Locally stored "Eclipse launch configurations"
*.launch

# PyDev specific (Python IDE for Eclipse)
*.pydevproject

# CDT-specific (C/C++ Development Tooling)
.cproject

# CDT- autotools
.autotools

# Java-specific
*.class
*.log
*.ctxt
.mtj.tmp/
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar
hs_err_pid*

# OS specific files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log
log/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test
.env.production
.env.local

# Application specific
application-local.yml
application-local.yaml
application-local.properties
application-dev.yml
application-dev.yaml
application-dev.properties

# Database
*.db
*.sqlite
*.sqlite3
h2/

# Temporary files
*.tmp
*.temp
*~

# Backup files
*.bak
*.backup

# Cache
.cache/
*.cache

# Spring Boot
spring-boot-*.log

# JRebel
rebel.xml

# Code coverage
jacoco.exec
.jacoco/

# Sonar
.sonar/
.sonarqube/

# Maven Wrapper
!/.mvn/wrapper/maven-wrapper.jar

# Gradle Wrapper
!gradle/wrapper/gradle-wrapper.jar

# JetBrains IDEs
.idea/
*.iml
*.ipr
*.iws

# Generated files
generated/
target/generated-sources/
target/generated-test-sources/