<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试注册链接</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .test-link {
            display: inline-block;
            padding: 12px 24px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px;
            transition: background 0.3s;
        }
        .test-link:hover {
            background: #764ba2;
        }
        .info {
            margin-top: 20px;
            padding: 15px;
            background: #f0f0f0;
            border-radius: 5px;
            text-align: left;
        }
        .info h3 {
            margin-top: 0;
        }
        .info p {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>测试注册链接</h1>
        
        <a href="http://localhost:3002/register" class="test-link">
            直接链接到注册页面
        </a>
        
        <a href="http://localhost:3002/login" class="test-link">
            前往登录页面
        </a>
        
        <div class="info">
            <h3>测试说明：</h3>
            <p>1. 点击"直接链接到注册页面"应该能直接访问注册页面</p>
            <p>2. 点击"前往登录页面"进入登录页，然后点击"立即注册"</p>
            <p>3. 打开浏览器控制台查看是否有错误信息</p>
            <p>4. 如果路由不工作，页面会使用 window.location.href 作为备用方案</p>
        </div>
    </div>
</body>
</html>