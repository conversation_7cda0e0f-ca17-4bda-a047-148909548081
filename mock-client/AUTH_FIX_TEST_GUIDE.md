# 认证问题修复测试指南

## 修复的问题

### 1. Token存储和获取问题
- **问题**: 当用户不勾选"记住我"时，token存储在sessionStorage中，但`getToken()`方法只检查localStorage
- **修复**: 修改了`storage.js`中的`getToken()`方法，按顺序检查sessionStorage、localStorage和cookies

### 2. API响应数据结构问题
- **问题**: 登录API响应被双重包装，导致无法正确提取token和用户信息
- **修复**: 修正了`api.js`中的mockApi方法，正确处理后端响应的数据结构

## 测试步骤

### 1. 启动应用
```bash
# 启动后端服务
cd mock-service
mvn spring-boot:run

# 启动前端服务
cd mock-client
npm run dev
```

### 2. 测试登录流程

#### 测试场景1: 不勾选"记住我"
1. 打开浏览器开发者工具（F12）
2. 切换到Console标签页
3. 访问登录页面
4. 输入用户名和密码，**不要勾选"记住我"**
5. 点击登录按钮
6. 观察Console中的日志输出：
   - 应该看到 "Login response:" 日志
   - 应该看到 "Extracted token:" 日志显示token值
   - 应该看到 "Token stored, rememberMe: false"
   - 应该看到 "Verification - stored token:" 显示存储的token

7. 登录成功后，访问需要认证的页面
8. 在Console中应该看到：
   - "Request interceptor - token:" 显示找到的token
   - "Authorization header set: Bearer [token]"

9. 检查浏览器存储：
   - 打开Application/Storage标签
   - Session Storage中应该有token
   - Local Storage中不应该有token

#### 测试场景2: 勾选"记住我"
1. 先登出（如果已登录）
2. 重新登录，这次**勾选"记住我"**
3. 观察Console日志，应该看到 "rememberMe: true"
4. 检查浏览器存储：
   - Local Storage中应该有token
   - Cookies中也应该有token

#### 测试场景3: API请求认证
1. 登录成功后，尝试访问需要认证的API端点
2. 在Network标签中检查请求：
   - 请求头中应该包含 "Authorization: Bearer [token]"
   - 响应状态应该是200，而不是401

### 3. 验证修复效果

#### 成功标志
- ✅ 登录后能正常访问需要认证的页面
- ✅ 不再出现"Missing authentication data"错误
- ✅ API请求包含正确的Authorization header
- ✅ Token根据"记住我"选项正确存储在对应位置

#### 调试日志
如果仍有问题，检查以下日志：
1. **登录响应结构**：
   ```javascript
   // 正确的响应结构应该是：
   {
     code: 200,
     data: {
       token: "eyJhbGc...",
       refreshToken: "...",
       user: { ... }
     },
     message: "登录成功"
   }
   ```

2. **Token提取**：
   - auth.js第26行应该能正确解构token、refreshToken和user

3. **Token存储**：
   - 根据rememberMe值，token应该存储在正确的位置

4. **请求拦截器**：
   - api.js第19行应该能获取到token
   - 第24行应该正确设置Authorization header

## 常见问题排查

### 问题1: 仍然提示"Missing authentication data"
- 检查后端返回的数据结构是否符合预期
- 确认LoginResponse类的结构与前端期望一致

### 问题2: Token存在但请求仍然401
- 检查token格式是否正确（应该以"Bearer "开头）
- 验证后端JWT配置是否正确

### 问题3: 刷新页面后需要重新登录
- 如果没有勾选"记住我"，这是正常行为（sessionStorage会清空）
- 如果勾选了"记住我"但仍需重新登录，检查localStorage中的token

## 后端验证点

1. **JwtAuthenticationFilter**应该能正确解析Bearer token
2. **JwtUtils**的`extractToken`方法应该正确提取token（去掉"Bearer "前缀）
3. **AuthController**的login方法应该返回正确的LoginResponse结构

## 总结

本次修复主要解决了两个关键问题：
1. Token存储位置与获取逻辑不匹配
2. API响应数据结构被错误包装

通过以上测试步骤，可以验证认证流程是否正常工作。如果仍有问题，请根据Console日志和Network请求详情进行进一步排查。