// Application configuration
export const config = {
  api: {
    baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api',
    timeout: parseInt(import.meta.env.VITE_API_TIMEOUT) || 30000,
  },
  
  oauth: {
    google: {
      clientId: import.meta.env.VITE_OAUTH_GOOGLE_CLIENT_ID,
      redirectUri: `${window.location.origin}/auth/callback/google`,
      scope: 'openid profile email',
    },
    facebook: {
      appId: import.meta.env.VITE_OAUTH_FACEBOOK_APP_ID,
      redirectUri: `${window.location.origin}/auth/callback/facebook`,
      scope: 'public_profile,email',
    },
    github: {
      clientId: import.meta.env.VITE_OAUTH_GITHUB_CLIENT_ID,
      redirectUri: `${window.location.origin}/auth/callback/github`,
      scope: 'user:email',
    },
    microsoft: {
      clientId: import.meta.env.VITE_OAUTH_MICROSOFT_CLIENT_ID,
      redirectUri: `${window.location.origin}/auth/callback/microsoft`,
      scope: 'openid profile email',
    },
  },
  
  app: {
    name: import.meta.env.VITE_APP_NAME || 'Mock Service',
    version: import.meta.env.VITE_APP_VERSION || '1.0.0',
    description: import.meta.env.VITE_APP_DESCRIPTION || 'Professional Mock Service Platform',
  },
  
  security: {
    enableMFA: import.meta.env.VITE_ENABLE_MFA === 'true',
    sessionTimeout: parseInt(import.meta.env.VITE_SESSION_TIMEOUT) || 3600000, // 1 hour
    rememberMeDuration: parseInt(import.meta.env.VITE_REMEMBER_ME_DURATION) || 604800000, // 7 days
  },
  
  features: {
    enableSocialLogin: import.meta.env.VITE_ENABLE_SOCIAL_LOGIN === 'true',
    enableEmailVerification: import.meta.env.VITE_ENABLE_EMAIL_VERIFICATION === 'true',
    enablePasswordRecovery: import.meta.env.VITE_ENABLE_PASSWORD_RECOVERY === 'true',
  },
  
  validation: {
    password: {
      minLength: 8,
      maxLength: 128,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: true,
    },
    username: {
      minLength: 3,
      maxLength: 30,
      pattern: /^[a-zA-Z0-9_-]+$/,
    },
    email: {
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    },
  },
  
  routes: {
    home: '/',
    login: '/login',
    register: '/register',
    forgotPassword: '/forgot-password',
    resetPassword: '/reset-password',
    emailVerification: '/verify-email',
    dashboard: '/dashboard',
    profile: '/profile',
    settings: '/settings',
  },
  
  storage: {
    tokenKey: 'auth_token',
    refreshTokenKey: 'refresh_token',
    userKey: 'user_data',
    rememberMeKey: 'remember_me',
    localeKey: 'locale',
    themeKey: 'theme',
  },
};

export default config;