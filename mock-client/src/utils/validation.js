import { config } from '@/config';

/**
 * 密码验证工具
 */
export const passwordValidation = {
  /**
   * 验证密码强度
   * @param {string} password - 要验证的密码
   * @returns {object} 包含强度分数和错误的验证结果
   */
  validate(password) {
    const errors = [];
    let strength = 0;
    
    if (!password) {
      return { valid: false, errors: ['密码不能为空'], strength: 0 };
    }
    
    // 长度验证
    if (password.length < config.validation.password.minLength) {
      errors.push(`密码长度至少为 ${config.validation.password.minLength} 个字符`);
    } else if (password.length > config.validation.password.maxLength) {
      errors.push(`密码长度不能超过 ${config.validation.password.maxLength} 个字符`);
    } else {
      strength += 20;
    }
    
    // 大写字母验证
    if (config.validation.password.requireUppercase && !/[A-Z]/.test(password)) {
      errors.push('密码必须包含至少一个大写字母');
    } else {
      strength += 20;
    }
    
    // 小写字母验证
    if (config.validation.password.requireLowercase && !/[a-z]/.test(password)) {
      errors.push('密码必须包含至少一个小写字母');
    } else {
      strength += 20;
    }
    
    // 数字验证
    if (config.validation.password.requireNumbers && !/\d/.test(password)) {
      errors.push('密码必须包含至少一个数字');
    } else {
      strength += 20;
    }
    
    // 特殊字符验证
    if (config.validation.password.requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('密码必须包含至少一个特殊字符');
    } else {
      strength += 20;
    }
    
    return {
      valid: errors.length === 0,
      errors,
      strength,
      strengthLabel: this.getStrengthLabel(strength),
    };
  },
  
  /**
   * 获取密码强度标签
   * @param {number} strength - 强度分数 (0-100)
   * @returns {string} 强度标签
   */
  getStrengthLabel(strength) {
    if (strength >= 80) return '很强';
    if (strength >= 60) return '强';
    if (strength >= 40) return '中等';
    if (strength >= 20) return '弱';
    return '很弱';
  },
  
  /**
   * 获取密码强度颜色
   * @param {number} strength - 强度分数 (0-100)
   * @returns {string} Tailwind 颜色类
   */
  getStrengthColor(strength) {
    if (strength >= 80) return 'bg-green-500';
    if (strength >= 60) return 'bg-blue-500';
    if (strength >= 40) return 'bg-yellow-500';
    if (strength >= 20) return 'bg-orange-500';
    return 'bg-red-500';
  },
};

/**
 * 邮箱验证
 * @param {string} email - 要验证的邮箱
 * @returns {object} 验证结果
 */
export const validateEmail = (email) => {
  if (!email) {
    return { valid: false, error: '邮箱不能为空' };
  }
  
  if (!config.validation.email.pattern.test(email)) {
    return { valid: false, error: '请输入有效的邮箱地址' };
  }
  
  return { valid: true };
};

/**
 * 用户名验证
 * @param {string} username - 要验证的用户名
 * @returns {object} 验证结果
 */
export const validateUsername = (username) => {
  if (!username) {
    return { valid: false, error: '用户名不能为空' };
  }
  
  if (username.length < config.validation.username.minLength) {
    return {
      valid: false,
      error: `用户名长度至少为 ${config.validation.username.minLength} 个字符`
    };
  }
  
  if (username.length > config.validation.username.maxLength) {
    return {
      valid: false,
      error: `用户名长度不能超过 ${config.validation.username.maxLength} 个字符`
    };
  }
  
  if (!config.validation.username.pattern.test(username)) {
    return {
      valid: false,
      error: '用户名只能包含字母、数字、下划线和连字符'
    };
  }
  
  return { valid: true };
};

/**
 * 电话号码验证
 * @param {string} phone - 要验证的电话号码
 * @returns {object} 验证结果
 */
export const validatePhone = (phone) => {
  if (!phone) {
    return { valid: true }; // 电话号码是可选的
  }
  
  // 基本的国际电话号码验证
  const phonePattern = /^\+?[1-9]\d{1,14}$/;
  
  if (!phonePattern.test(phone.replace(/[\s()-]/g, ''))) {
    return { valid: false, error: '请输入有效的电话号码' };
  }
  
  return { valid: true };
};

/**
 * 表单验证助手
 * @param {object} fields - 要验证的字段
 * @param {object} rules - 验证规则
 * @returns {object} 验证结果
 */
export const validateForm = (fields, rules) => {
  const errors = {};
  let isValid = true;
  
  Object.keys(rules).forEach(fieldName => {
    const fieldValue = fields[fieldName];
    const fieldRules = rules[fieldName];
    
    // 必填验证
    if (fieldRules.required && !fieldValue) {
      errors[fieldName] = fieldRules.message || `${fieldName} 不能为空`;
      isValid = false;
      return;
    }
    
    // 自定义验证函数
    if (fieldRules.validate && fieldValue) {
      const result = fieldRules.validate(fieldValue);
      if (!result.valid) {
        errors[fieldName] = result.error || fieldRules.message || `${fieldName} 格式无效`;
        isValid = false;
      }
    }
    
    // 模式验证
    if (fieldRules.pattern && fieldValue) {
      if (!fieldRules.pattern.test(fieldValue)) {
        errors[fieldName] = fieldRules.message || `${fieldName} 格式无效`;
        isValid = false;
      }
    }
    
    // 最小长度验证
    if (fieldRules.minLength && fieldValue) {
      if (fieldValue.length < fieldRules.minLength) {
        errors[fieldName] = fieldRules.message || `${fieldName} 长度至少为 ${fieldRules.minLength} 个字符`;
        isValid = false;
      }
    }
    
    // 最大长度验证
    if (fieldRules.maxLength && fieldValue) {
      if (fieldValue.length > fieldRules.maxLength) {
        errors[fieldName] = fieldRules.message || `${fieldName} 长度不能超过 ${fieldRules.maxLength} 个字符`;
        isValid = false;
      }
    }
    
    // 匹配验证（用于密码确认）
    if (fieldRules.match && fieldValue) {
      if (fieldValue !== fields[fieldRules.match]) {
        errors[fieldName] = fieldRules.message || `${fieldName} 不匹配`;
        isValid = false;
      }
    }
  });
  
  return { isValid, errors };
};

/**
 * 简单密码验证包装器
 * @param {string} password - 要验证的密码
 * @returns {object} 验证结果
 */
export const validatePassword = (password) => {
  const result = passwordValidation.validate(password);
  return {
    valid: result.valid,
    error: result.errors.length > 0 ? result.errors[0] : null,
    strength: result.strength,
    strengthLabel: result.strengthLabel
  };
};

export default {
  passwordValidation,
  validateEmail,
  validateUsername,
  validatePassword,
  validatePhone,
  validateForm,
};