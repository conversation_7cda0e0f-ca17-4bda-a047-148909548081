import { config } from '@/config';

/**
 * 本地存储包装器，支持 JSON
 */
class StorageService {
  /**
   * 从存储中获取项目
   * @param {string} key - 存储键
   * @param {any} defaultValue - 键不存在时的默认值
   * @returns {any} 存储的值或默认值
   */
  get(key, defaultValue = null) {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
      console.error(`从存储读取错误: ${key}`, error);
      return defaultValue;
    }
  }
  
  /**
   * 在存储中设置项目
   * @param {string} key - 存储键
   * @param {any} value - 要存储的值
   * @returns {boolean} 成功状态
   */
  set(key, value) {
    try {
      localStorage.setItem(key, JSON.stringify(value));
      return true;
    } catch (error) {
      console.error(`写入存储错误: ${key}`, error);
      return false;
    }
  }
  
  /**
   * 从存储中移除项目
   * @param {string} key - 存储键
   * @returns {boolean} 成功状态
   */
  remove(key) {
    try {
      localStorage.removeItem(key);
      return true;
    } catch (error) {
      console.error(`从存储移除错误: ${key}`, error);
      return false;
    }
  }
  
  /**
   * 清除所有存储
   * @returns {boolean} 成功状态
   */
  clear() {
    try {
      localStorage.clear();
      return true;
    } catch (error) {
      console.error('清除存储错误', error);
      return false;
    }
  }
  
  /**
   * 检查键是否存在于存储中
   * @param {string} key - 存储键
   * @returns {boolean} 存在状态
   */
  has(key) {
    return localStorage.getItem(key) !== null;
  }
}

/**
 * 会话存储包装器，支持 JSON
 */
class SessionStorageService {
  /**
   * 从会话存储中获取项目
   * @param {string} key - 存储键
   * @param {any} defaultValue - 键不存在时的默认值
   * @returns {any} 存储的值或默认值
   */
  get(key, defaultValue = null) {
    try {
      const item = sessionStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
      console.error(`从会话存储读取错误: ${key}`, error);
      return defaultValue;
    }
  }
  
  /**
   * 在会话存储中设置项目
   * @param {string} key - 存储键
   * @param {any} value - 要存储的值
   * @returns {boolean} 成功状态
   */
  set(key, value) {
    try {
      sessionStorage.setItem(key, JSON.stringify(value));
      return true;
    } catch (error) {
      console.error(`写入会话存储错误: ${key}`, error);
      return false;
    }
  }
  
  /**
   * 从会话存储中移除项目
   * @param {string} key - 存储键
   * @returns {boolean} 成功状态
   */
  remove(key) {
    try {
      sessionStorage.removeItem(key);
      return true;
    } catch (error) {
      console.error(`从会话存储移除错误: ${key}`, error);
      return false;
    }
  }
  
  /**
   * 清除所有会话存储
   * @returns {boolean} 成功状态
   */
  clear() {
    try {
      sessionStorage.clear();
      return true;
    } catch (error) {
      console.error('清除会话存储错误', error);
      return false;
    }
  }
  
  /**
   * 检查键是否存在于会话存储中
   * @param {string} key - 存储键
   * @returns {boolean} 存在状态
   */
  has(key) {
    return sessionStorage.getItem(key) !== null;
  }
}

/**
 * Cookie 存储包装器
 */
class CookieService {
  /**
   * 获取 Cookie 值
   * @param {string} name - Cookie 名称
   * @returns {string|null} Cookie 值或 null
   */
  get(name) {
    const nameEQ = name + '=';
    const ca = document.cookie.split(';');
    for (let i = 0; i < ca.length; i++) {
      let c = ca[i];
      while (c.charAt(0) === ' ') c = c.substring(1, c.length);
      if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
    }
    return null;
  }
  
  /**
   * 设置 Cookie 值
   * @param {string} name - Cookie 名称
   * @param {string} value - Cookie 值
   * @param {number} days - 过期天数
   * @param {object} options - 额外的 Cookie 选项
   */
  set(name, value, days = 7, options = {}) {
    let expires = '';
    if (days) {
      const date = new Date();
      date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
      expires = '; expires=' + date.toUTCString();
    }
    
    const path = options.path || '/';
    const domain = options.domain ? `; domain=${options.domain}` : '';
    const secure = options.secure ? '; secure' : '';
    const sameSite = options.sameSite ? `; samesite=${options.sameSite}` : '; samesite=lax';
    
    document.cookie = `${name}=${value}${expires}; path=${path}${domain}${secure}${sameSite}`;
  }
  
  /**
   * 移除 Cookie
   * @param {string} name - Cookie 名称
   * @param {object} options - Cookie 选项
   */
  remove(name, options = {}) {
    this.set(name, '', -1, options);
  }
  
  /**
   * 检查 Cookie 是否存在
   * @param {string} name - Cookie 名称
   * @returns {boolean} 存在状态
   */
  has(name) {
    return this.get(name) !== null;
  }
}

// 认证令牌管理
export const tokenStorage = {
  /**
   * 获取认证令牌
   * @returns {string|null} 令牌或 null
   */
  getToken() {
    // 首先检查会话存储（用于非记住的会话）
    let token = session.get(config.storage.tokenKey);
    // 如果会话中没有，检查本地存储（用于记住的会话）
    if (!token) {
      token = storage.get(config.storage.tokenKey);
    }
    // 也检查 cookies 作为后备
    if (!token) {
      token = cookies.get(config.storage.tokenKey);
    }
    return token;
  },
  
  /**
   * 设置认证令牌
   * @param {string} token - 认证令牌
   * @param {boolean} rememberMe - 是否持久化令牌
   */
  setToken(token, rememberMe = false) {
    if (rememberMe) {
      storage.set(config.storage.tokenKey, token);
      cookies.set(config.storage.tokenKey, token, 7, { secure: true, sameSite: 'strict' });
    } else {
      session.set(config.storage.tokenKey, token);
    }
  },
  
  /**
   * 获取刷新令牌
   * @returns {string|null} 刷新令牌或 null
   */
  getRefreshToken() {
    return storage.get(config.storage.refreshTokenKey);
  },
  
  /**
   * 设置刷新令牌
   * @param {string} token - 刷新令牌
   */
  setRefreshToken(token) {
    storage.set(config.storage.refreshTokenKey, token);
  },
  
  /**
   * 清除所有令牌
   */
  clearTokens() {
    storage.remove(config.storage.tokenKey);
    storage.remove(config.storage.refreshTokenKey);
    session.remove(config.storage.tokenKey);
    cookies.remove(config.storage.tokenKey);
  },
  
  /**
   * 检查用户是否已认证
   * @returns {boolean} 认证状态
   */
  isAuthenticated() {
    const token = this.getToken();
    return token !== null && token !== undefined && token !== '';
  },
};

// 用户数据管理
export const userStorage = {
  /**
   * 获取用户数据
   * @returns {object|null} 用户数据或 null
   */
  getUser() {
    return storage.get(config.storage.userKey);
  },
  
  /**
   * 设置用户数据
   * @param {object} userData - 用户数据
   */
  setUser(userData) {
    storage.set(config.storage.userKey, userData);
  },
  
  /**
   * 更新用户数据
   * @param {object} updates - 部分用户数据更新
   */
  updateUser(updates) {
    const currentUser = this.getUser();
    if (currentUser) {
      this.setUser({ ...currentUser, ...updates });
    }
  },
  
  /**
   * 清除用户数据
   */
  clearUser() {
    storage.remove(config.storage.userKey);
  },
};

// 导出实例
export const storage = new StorageService();
export const session = new SessionStorageService();
export const cookies = new CookieService();

export default {
  storage,
  session,
  cookies,
  tokenStorage,
  userStorage,
};