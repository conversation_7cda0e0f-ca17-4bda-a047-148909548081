import { mockApi } from './api';
import { tokenStorage, userStorage } from '@/utils/storage';
import { config } from '@/config';

/**
 * 认证服务
 */
class AuthService {
  /**
   * 用户登录
   * @param {object} credentials - 登录凭据
   * @param {boolean} rememberMe - 是否记住用户
   * @returns {Promise} 登录响应
   */
  async login(credentials, rememberMe = false) {
    try {
      const response = await mockApi.login(credentials);
      
      console.log('登录响应:', response); // 调试日志
      
      // 检查响应是否具有预期结构
      if (!response || !response.data) {
        throw new Error('服务器响应无效');
      }
      
      // 提取数据 - 后端返回 accessToken，而不是 token
      const { accessToken, refreshToken, userInfo, tokenType } = response.data;
      
      console.log('提取的 accessToken:', accessToken); // 调试日志
      console.log('提取的 userInfo:', userInfo); // 调试日志
      
      // 验证必需字段
      if (!accessToken || !userInfo) {
        throw new Error('缺少认证数据');
      }
      
      // 存储令牌
      tokenStorage.setToken(accessToken, rememberMe);
      console.log('令牌已存储, rememberMe:', rememberMe); // 调试日志
      
      // 验证令牌是否已存储
      const storedToken = tokenStorage.getToken();
      console.log('验证 - 已存储的令牌:', storedToken); // 调试日志
      
      if (refreshToken) {
        tokenStorage.setRefreshToken(refreshToken);
      }
      
      // 存储用户数据
      userStorage.setUser(userInfo);
      
      // 设置记住我标志
      if (rememberMe) {
        localStorage.setItem(config.storage.rememberMeKey, 'true');
      }
      
      return {
        success: true,
        user: userInfo,
        message: '登录成功',
      };
    } catch (error) {
      console.error('登录错误:', error);
      
      // 从不同的错误结构中提取错误消息
      let errorMessage = '登录失败';
      
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.data?.message) {
        errorMessage = error.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      return {
        success: false,
        message: errorMessage,
        error,
      };
    }
  }
  
  /**
   * 用户注册
   * @param {object} userData - 注册数据
   * @returns {Promise} 注册响应
   */
  async register(userData) {
    try {
      const response = await mockApi.register(userData);
      
      // 检查响应是否具有预期结构
      if (!response || !response.data) {
        throw new Error('服务器响应无效');
      }
      
      const { accessToken, refreshToken, userInfo, requiresVerification } = response.data;
      
      if (!requiresVerification && accessToken) {
        // 如果不需要邮箱验证则自动登录
        tokenStorage.setToken(accessToken);
        if (refreshToken) {
          tokenStorage.setRefreshToken(refreshToken);
        }
        if (userInfo) {
          userStorage.setUser(userInfo);
        }
      }
      
      return {
        success: true,
        user: userInfo,
        requiresVerification,
        message: requiresVerification
          ? '注册成功。请检查您的邮箱以验证您的账户。'
          : '注册成功',
      };
    } catch (error) {
      console.error('注册错误:', error);
      
      // 从不同的错误结构中提取错误消息
      let errorMessage = '注册失败';
      
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.data?.message) {
        errorMessage = error.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      return {
        success: false,
        message: errorMessage,
        error,
      };
    }
  }
  
  /**
   * 用户登出
   * @returns {Promise} 登出响应
   */
  async logout() {
    try {
      // 调用登出 API
      await mockApi.logout();
    } catch (error) {
      console.error('登出 API 错误:', error);
    } finally {
      // 无论 API 响应如何都清除本地数据
      this.clearAuthData();
    }
    
    return {
      success: true,
      message: '登出成功',
    };
  }
  
  /**
   * 刷新认证令牌
   * @returns {Promise} 刷新响应
   */
  async refreshToken() {
    try {
      const refreshToken = tokenStorage.getRefreshToken();
      if (!refreshToken) {
        throw new Error('没有可用的刷新令牌');
      }
      
      const response = await mockApi.refreshToken(refreshToken);
      
      // 检查响应是否具有预期结构
      if (!response || !response.data) {
        throw new Error('服务器响应无效');
      }
      
      const { accessToken, refreshToken: newRefreshToken } = response.data;
      
      // 更新令牌
      tokenStorage.setToken(accessToken);
      if (newRefreshToken) {
        tokenStorage.setRefreshToken(newRefreshToken);
      }
      
      return {
        success: true,
        token: accessToken,
      };
    } catch (error) {
      console.error('令牌刷新错误:', error);
      this.clearAuthData();
      
      // 从不同的错误结构中提取错误消息
      let errorMessage = '令牌刷新失败';
      
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.data?.message) {
        errorMessage = error.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      return {
        success: false,
        message: errorMessage,
        error,
      };
    }
  }
  
  /**
   * 忘记密码
   * @param {string} email - 用户邮箱
   * @returns {Promise} 响应
   */
  async forgotPassword(email) {
    try {
      await mockApi.forgotPassword(email);
      return {
        success: true,
        message: '密码重置说明已发送到您的邮箱',
      };
    } catch (error) {
      return {
        success: false,
        message: error.message || '发送密码重置邮件失败',
        error,
      };
    }
  }
  
  /**
   * 重置密码
   * @param {string} token - 重置令牌
   * @param {string} password - 新密码
   * @returns {Promise} 响应
   */
  async resetPassword(token, password) {
    try {
      await mockApi.resetPassword(token, password);
      return {
        success: true,
        message: '密码重置成功',
      };
    } catch (error) {
      return {
        success: false,
        message: error.message || '密码重置失败',
        error,
      };
    }
  }
  
  /**
   * 验证邮箱
   * @param {string} token - 验证令牌
   * @returns {Promise} 响应
   */
  async verifyEmail(token) {
    try {
      const response = await mockApi.verifyEmail(token);
      
      // 检查响应是否具有预期结构
      if (!response || !response.data) {
        throw new Error('服务器响应无效');
      }
      
      const { userInfo, accessToken, refreshToken } = response.data;
      
      // 验证后自动登录
      if (accessToken) {
        tokenStorage.setToken(accessToken);
        if (refreshToken) {
          tokenStorage.setRefreshToken(refreshToken);
        }
        if (userInfo) {
          userStorage.setUser(userInfo);
        }
      }
      
      return {
        success: true,
        message: '邮箱验证成功',
        user: userInfo,
      };
    } catch (error) {
      console.error('邮箱验证错误:', error);
      
      // 从不同的错误结构中提取错误消息
      let errorMessage = '邮箱验证失败';
      
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.data?.message) {
        errorMessage = error.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      return {
        success: false,
        message: errorMessage,
        error,
      };
    }
  }
  
  /**
   * 重新发送验证邮件
   * @param {string} email - 用户邮箱
   * @returns {Promise} 响应
   */
  async resendVerification(email) {
    try {
      await mockApi.resendVerification(email);
      return {
        success: true,
        message: '验证邮件已重新发送',
      };
    } catch (error) {
      return {
        success: false,
        message: error.message || '重新发送验证邮件失败',
        error,
      };
    }
  }
  
  /**
   * 获取当前用户
   * @returns {Promise} 用户数据
   */
  async getCurrentUser() {
    try {
      const response = await mockApi.getCurrentUser();
      
      // 检查响应是否具有预期结构
      if (!response || !response.data) {
        throw new Error('服务器响应无效');
      }
      
      const user = response.data;
      
      // 更新存储的用户数据
      userStorage.setUser(user);
      
      return {
        success: true,
        user,
      };
    } catch (error) {
      console.error('获取当前用户错误:', error);
      
      // 从不同的错误结构中提取错误消息
      let errorMessage = '获取用户数据失败';
      
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.data?.message) {
        errorMessage = error.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      return {
        success: false,
        message: errorMessage,
        error,
      };
    }
  }
  
  /**
   * 检查用户是否已认证
   * @returns {boolean} 认证状态
   */
  isAuthenticated() {
    return tokenStorage.isAuthenticated();
  }
  
  /**
   * 获取存储的用户数据
   * @returns {object|null} 用户数据
   */
  getUser() {
    return userStorage.getUser();
  }
  
  /**
   * 清除认证数据
   */
  clearAuthData() {
    tokenStorage.clearTokens();
    userStorage.clearUser();
    localStorage.removeItem(config.storage.rememberMeKey);
  }
  
  /**
   * 初始化认证
   * @returns {Promise} 初始化结果
   */
  async initialize() {
    // 检查用户是否有有效令牌
    if (this.isAuthenticated()) {
      // 尝试获取当前用户数据
      const result = await this.getCurrentUser();
      if (!result.success) {
        // 令牌无效，清除认证数据
        this.clearAuthData();
      }
      return result;
    }
    
    return {
      success: false,
      message: '未认证',
    };
  }
}

// 创建单例实例
const authService = new AuthService();

export default authService;