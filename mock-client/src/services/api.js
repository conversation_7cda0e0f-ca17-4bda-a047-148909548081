import axios from 'axios';
import { config } from '@/config';
import { tokenStorage } from '@/utils/storage';
import router from '@/router';

// 创建 axios 实例
const api = axios.create({
  baseURL: config.api.baseURL,
  timeout: config.api.timeout,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (requestConfig) => {
    // 添加认证令牌到请求头
    const token = tokenStorage.getToken();
    console.log('请求拦截器 - 令牌:', token); // 调试日志
    console.log('请求 URL:', requestConfig.url); // 调试日志
    
    if (token) {
      requestConfig.headers.Authorization = `Bearer ${token}`;
      console.log('Authorization 头已设置:', requestConfig.headers.Authorization); // 调试日志
    } else {
      console.log('请求未找到令牌'); // 调试日志
    }
    
    // 添加时间戳防止缓存
    if (requestConfig.method === 'get') {
      requestConfig.params = {
        ...requestConfig.params,
        _t: Date.now(),
      };
    }
    
    return requestConfig;
  },
  (error) => {
    console.error('请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    // 处理成功响应
    // 检查响应是否具有预期结构
    if (response.data) {
      // 如果后端返回带有 code/success 字段的标准响应格式
      if (response.data.code !== undefined) {
        // 处理后端业务逻辑错误
        if (response.data.code !== 200 && response.data.code !== 0) {
          // 来自后端的业务错误
          return Promise.reject({
            message: response.data.message || response.data.msg || '请求失败',
            code: response.data.code,
            data: response.data.data,
            success: false
          });
        }
        // 成功响应 - 返回整个响应以保持兼容性
        return response.data;
      }
      // 如果没有标准格式则按原样返回数据
      return response.data;
    }
    return response;
  },
  async (error) => {
    const originalRequest = error.config;
    
    // 处理网络错误
    if (!error.response) {
      return Promise.reject({
        message: '网络错误，请检查您的网络连接',
        code: 'NETWORK_ERROR',
        status: 0,
        success: false
      });
    }
    
    // 处理 401 未授权
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      // 尝试刷新令牌
      const refreshToken = tokenStorage.getRefreshToken();
      if (refreshToken && !originalRequest.url.includes('/auth/refresh')) {
        try {
          const response = await api.post('/auth/refresh', { refreshToken });
          // 处理 'token' 和 'accessToken' 字段名以保持兼容性
          const { token, accessToken } = response.data || response;
          const authToken = token || accessToken;
          
          if (authToken) {
            tokenStorage.setToken(authToken);
            originalRequest.headers.Authorization = `Bearer ${authToken}`;
            return api(originalRequest);
          }
        } catch (refreshError) {
          // 刷新失败，重定向到登录页面
          tokenStorage.clearTokens();
          router.push('/login?message=' + encodeURIComponent('会话已过期，请重新登录'));
          return Promise.reject({
            message: '会话已过期，请重新登录',
            code: 'SESSION_EXPIRED',
            status: 401,
            success: false
          });
        }
      } else {
        // 没有刷新令牌，重定向到登录页面
        tokenStorage.clearTokens();
        router.push('/login?message=' + encodeURIComponent('请先登录'));
        return Promise.reject({
          message: '请先登录',
          code: 'UNAUTHORIZED',
          status: 401,
          success: false
        });
      }
    }
    
    // 处理 403 禁止访问
    if (error.response?.status === 403) {
      return Promise.reject({
        message: error.response?.data?.message || '您没有权限执行此操作',
        code: 'FORBIDDEN',
        status: 403,
        success: false,
        data: error.response?.data
      });
    }
    
    // 处理 404 未找到
    if (error.response?.status === 404) {
      return Promise.reject({
        message: error.response?.data?.message || '请求的资源不存在',
        code: 'NOT_FOUND',
        status: 404,
        success: false,
        data: error.response?.data
      });
    }
    
    // 处理 500 服务器错误
    if (error.response?.status >= 500) {
      return Promise.reject({
        message: error.response?.data?.message || '服务器错误，请稍后重试',
        code: 'SERVER_ERROR',
        status: error.response.status,
        success: false,
        data: error.response?.data
      });
    }
    
    // 处理其他错误
    const errorMessage = error.response?.data?.message ||
                        error.response?.data?.msg ||
                        error.message ||
                        '请求失败';
    const errorCode = error.response?.data?.code ||
                     error.response?.status ||
                     'UNKNOWN';
    
    return Promise.reject({
      message: errorMessage,
      code: errorCode,
      status: error.response?.status,
      success: false,
      data: error.response?.data,
    });
  }
);

// API 方法
export const apiService = {
  /**
   * GET 请求
   * @param {string} url - API 端点
   * @param {object} params - 查询参数
   * @param {object} config - 额外的 axios 配置
   * @returns {Promise} 响应数据
   */
  get(url, params = {}, config = {}) {
    return api.get(url, { params, ...config });
  },
  
  /**
   * POST 请求
   * @param {string} url - API 端点
   * @param {object} data - 请求体
   * @param {object} config - 额外的 axios 配置
   * @returns {Promise} 响应数据
   */
  post(url, data = {}, config = {}) {
    return api.post(url, data, config);
  },
  
  /**
   * PUT 请求
   * @param {string} url - API 端点
   * @param {object} data - 请求体
   * @param {object} config - 额外的 axios 配置
   * @returns {Promise} 响应数据
   */
  put(url, data = {}, config = {}) {
    return api.put(url, data, config);
  },
  
  /**
   * PATCH 请求
   * @param {string} url - API 端点
   * @param {object} data - 请求体
   * @param {object} config - 额外的 axios 配置
   * @returns {Promise} 响应数据
   */
  patch(url, data = {}, config = {}) {
    return api.patch(url, data, config);
  },
  
  /**
   * DELETE 请求
   * @param {string} url - API 端点
   * @param {object} config - 额外的 axios 配置
   * @returns {Promise} 响应数据
   */
  delete(url, config = {}) {
    return api.delete(url, config);
  },
  
  /**
   * 上传文件
   * @param {string} url - API 端点
   * @param {FormData} formData - 包含文件的表单数据
   * @param {function} onProgress - 上传进度回调
   * @returns {Promise} 响应数据
   */
  upload(url, formData, onProgress) {
    return api.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress) {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(percentCompleted);
        }
      },
    });
  },
  
  /**
   * 下载文件
   * @param {string} url - API 端点
   * @param {string} filename - 下载文件名
   * @returns {Promise} 响应数据
   */
  async download(url, filename) {
    const response = await api.get(url, {
      responseType: 'blob',
    });
    
    // 创建下载链接
    const downloadUrl = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.setAttribute('download', filename);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(downloadUrl);
    
    return response;
  },
};

// Mock API 端点（用于开发）
export const mockApi = {
  // 认证端点 - 包装响应以确保一致的结构
  login: async (credentials) => {
    try {
      const response = await apiService.post('/auth/login', credentials);
      // 响应已经从 apiService 获得了正确的结构
      // 它返回包含 { code, data, message } 的 response.data
      // 我们需要为认证服务返回实际的数据部分
      if (response && response.data) {
        return { data: response.data };
      }
      return { data: response };
    } catch (error) {
      // 重新抛出一致的错误结构
      throw error;
    }
  },
  register: async (userData) => {
    try {
      const response = await apiService.post('/auth/register', userData);
      if (response && response.data) {
        return { data: response.data };
      }
      return { data: response };
    } catch (error) {
      throw error;
    }
  },
  logout: () => apiService.post('/auth/logout'),
  refreshToken: async (refreshToken) => {
    try {
      const response = await apiService.post('/auth/refresh', { refreshToken });
      if (response && response.data) {
        return { data: response.data };
      }
      return { data: response };
    } catch (error) {
      throw error;
    }
  },
  forgotPassword: (email) => apiService.post('/auth/forgot-password', { email }),
  resetPassword: (token, password) => apiService.post('/auth/reset-password', { token, password }),
  verifyEmail: async (token) => {
    try {
      const response = await apiService.post('/auth/verify-email', { token });
      if (response && response.data) {
        return { data: response.data };
      }
      return { data: response };
    } catch (error) {
      throw error;
    }
  },
  resendVerification: (email) => apiService.post('/auth/resend-verification', { email }),
  
  // OAuth endpoints
  oauthLogin: (provider) => apiService.get(`/auth/oauth/${provider}`),
  oauthCallback: (provider, code) => apiService.post(`/auth/oauth/${provider}/callback`, { code }),
  
  // User endpoints
  getCurrentUser: async () => {
    try {
      const response = await apiService.get('/auth/current-user');
      if (response && response.data) {
        return { data: response.data };
      }
      return { data: response };
    } catch (error) {
      throw error;
    }
  },
  updateProfile: (data) => apiService.put('/user/profile', data),
  changePassword: (data) => apiService.post('/user/change-password', data),
  uploadAvatar: (formData) => apiService.upload('/user/avatar', formData),
  deleteAccount: () => apiService.delete('/user/account'),
  
  // MFA endpoints
  enableMFA: () => apiService.post('/auth/mfa/enable'),
  disableMFA: () => apiService.post('/auth/mfa/disable'),
  verifyMFA: (code) => apiService.post('/auth/mfa/verify', { code }),
  generateBackupCodes: () => apiService.post('/auth/mfa/backup-codes'),
  
  // Session endpoints
  getSessions: () => apiService.get('/user/sessions'),
  revokeSession: (sessionId) => apiService.delete(`/user/sessions/${sessionId}`),
  revokeAllSessions: () => apiService.delete('/user/sessions'),
  
  // Menu and Permission endpoints
  getUserMenus: async (userId) => {
    try {
      const response = await apiService.get(`/system/menu/user/${userId}`);
      if (response && response.data) {
        return { data: response.data };
      }
      return { data: response };
    } catch (error) {
      throw error;
    }
  },
  getUserPermissions: async (userId) => {
    try {
      const response = await apiService.get(`/api/user/${userId}/permissions`);
      if (response && response.data) {
        return { data: response.data };
      }
      return { data: response };
    } catch (error) {
      throw error;
    }
  },
  getUserButtonPermissions: async (userId) => {
    try {
      const response = await apiService.get(`/api/user/${userId}/button-permissions`);
      if (response && response.data) {
        return { data: response.data };
      }
      return { data: response };
    } catch (error) {
      throw error;
    }
  },
};

export default api;