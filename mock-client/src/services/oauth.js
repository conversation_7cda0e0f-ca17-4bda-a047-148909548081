import { config } from '@/config';
import { mockApi } from './api';
import { tokenStorage, userStorage } from '@/utils/storage';

/**
 * OAuth service for social login
 */
class OAuthService {
  constructor() {
    this.providers = {
      google: {
        name: 'Google',
        authUrl: 'https://accounts.google.com/o/oauth2/v2/auth',
        scope: config.oauth.google.scope,
        clientId: config.oauth.google.clientId,
        redirectUri: config.oauth.google.redirectUri,
        icon: 'google',
        color: 'bg-red-600 hover:bg-red-700',
      },
      facebook: {
        name: 'Facebook',
        authUrl: 'https://www.facebook.com/v12.0/dialog/oauth',
        scope: config.oauth.facebook.scope,
        clientId: config.oauth.facebook.appId,
        redirectUri: config.oauth.facebook.redirectUri,
        icon: 'facebook',
        color: 'bg-blue-600 hover:bg-blue-700',
      },
      github: {
        name: 'GitHub',
        authUrl: 'https://github.com/login/oauth/authorize',
        scope: config.oauth.github.scope,
        clientId: config.oauth.github.clientId,
        redirectUri: config.oauth.github.redirectUri,
        icon: 'github',
        color: 'bg-gray-800 hover:bg-gray-900',
      },
      microsoft: {
        name: 'Microsoft',
        authUrl: 'https://login.microsoftonline.com/common/oauth2/v2.0/authorize',
        scope: config.oauth.microsoft.scope,
        clientId: config.oauth.microsoft.clientId,
        redirectUri: config.oauth.microsoft.redirectUri,
        icon: 'microsoft',
        color: 'bg-blue-500 hover:bg-blue-600',
      },
    };
    
    // OAuth popup window reference
    this.popupWindow = null;
    this.popupInterval = null;
  }
  
  /**
   * Get available OAuth providers
   * @returns {array} List of providers
   */
  getProviders() {
    return Object.entries(this.providers)
      .filter(([key, provider]) => provider.clientId)
      .map(([key, provider]) => ({
        key,
        ...provider,
      }));
  }
  
  /**
   * Initiate OAuth login
   * @param {string} provider - Provider name
   * @param {boolean} linkAccount - Whether to link to existing account
   * @returns {Promise} Login result
   */
  async login(provider, linkAccount = false) {
    const providerConfig = this.providers[provider];
    
    if (!providerConfig) {
      return {
        success: false,
        message: `Unknown provider: ${provider}`,
      };
    }
    
    if (!providerConfig.clientId) {
      return {
        success: false,
        message: `${providerConfig.name} login is not configured`,
      };
    }
    
    try {
      // Generate state for CSRF protection
      const state = this.generateState();
      sessionStorage.setItem('oauth_state', state);
      sessionStorage.setItem('oauth_provider', provider);
      
      if (linkAccount) {
        sessionStorage.setItem('oauth_link_account', 'true');
      }
      
      // Build authorization URL
      const params = new URLSearchParams({
        client_id: providerConfig.clientId,
        redirect_uri: providerConfig.redirectUri,
        response_type: 'code',
        scope: providerConfig.scope,
        state: state,
      });
      
      // Add provider-specific parameters
      if (provider === 'google') {
        params.append('access_type', 'offline');
        params.append('prompt', 'consent');
      } else if (provider === 'microsoft') {
        params.append('response_mode', 'query');
      }
      
      const authUrl = `${providerConfig.authUrl}?${params.toString()}`;
      
      // Open OAuth popup
      const result = await this.openPopup(authUrl, providerConfig.name);
      
      if (result.success) {
        return await this.handleCallback(result.code, provider);
      }
      
      return result;
    } catch (error) {
      console.error('OAuth login error:', error);
      return {
        success: false,
        message: error.message || 'OAuth login failed',
        error,
      };
    }
  }
  
  /**
   * Handle OAuth callback
   * @param {string} code - Authorization code
   * @param {string} provider - Provider name
   * @returns {Promise} Callback result
   */
  async handleCallback(code, provider) {
    try {
      const response = await mockApi.oauthCallback(provider, code);
      const { token, refreshToken, user, isNewUser } = response.data;
      
      // Store tokens
      tokenStorage.setToken(token);
      if (refreshToken) {
        tokenStorage.setRefreshToken(refreshToken);
      }
      
      // Store user data
      userStorage.setUser(user);
      
      // Clear OAuth session data
      sessionStorage.removeItem('oauth_state');
      sessionStorage.removeItem('oauth_provider');
      sessionStorage.removeItem('oauth_link_account');
      
      return {
        success: true,
        user,
        isNewUser,
        message: isNewUser 
          ? 'Account created successfully' 
          : 'Login successful',
      };
    } catch (error) {
      return {
        success: false,
        message: error.message || 'OAuth callback failed',
        error,
      };
    }
  }
  
  /**
   * Link OAuth account to existing user
   * @param {string} provider - Provider name
   * @returns {Promise} Link result
   */
  async linkAccount(provider) {
    return this.login(provider, true);
  }
  
  /**
   * Unlink OAuth account
   * @param {string} provider - Provider name
   * @returns {Promise} Unlink result
   */
  async unlinkAccount(provider) {
    try {
      await mockApi.post(`/auth/oauth/${provider}/unlink`);
      return {
        success: true,
        message: `${this.providers[provider].name} account unlinked successfully`,
      };
    } catch (error) {
      return {
        success: false,
        message: error.message || 'Failed to unlink account',
        error,
      };
    }
  }
  
  /**
   * Open OAuth popup window
   * @param {string} url - Authorization URL
   * @param {string} title - Window title
   * @returns {Promise} Popup result
   */
  openPopup(url, title) {
    return new Promise((resolve) => {
      // Calculate popup position
      const width = 500;
      const height = 600;
      const left = window.screenX + (window.outerWidth - width) / 2;
      const top = window.screenY + (window.outerHeight - height) / 2;
      
      // Open popup window
      this.popupWindow = window.open(
        url,
        title,
        `width=${width},height=${height},left=${left},top=${top},toolbar=no,menubar=no`
      );
      
      if (!this.popupWindow) {
        resolve({
          success: false,
          message: 'Popup blocked. Please allow popups for this site.',
        });
        return;
      }
      
      // Monitor popup window
      this.popupInterval = setInterval(() => {
        try {
          // Check if popup is closed
          if (!this.popupWindow || this.popupWindow.closed) {
            this.cleanup();
            resolve({
              success: false,
              message: 'Authentication cancelled',
            });
            return;
          }
          
          // Check for callback URL
          const popupUrl = this.popupWindow.location.href;
          if (popupUrl.includes('/auth/callback/')) {
            // Extract code from URL
            const urlParams = new URLSearchParams(this.popupWindow.location.search);
            const code = urlParams.get('code');
            const error = urlParams.get('error');
            
            // Close popup
            this.popupWindow.close();
            this.cleanup();
            
            if (error) {
              resolve({
                success: false,
                message: error === 'access_denied' 
                  ? 'Access denied' 
                  : `Authentication failed: ${error}`,
              });
            } else if (code) {
              resolve({
                success: true,
                code,
              });
            } else {
              resolve({
                success: false,
                message: 'No authorization code received',
              });
            }
          }
        } catch (e) {
          // Cross-origin error, ignore
        }
      }, 500);
      
      // Timeout after 5 minutes
      setTimeout(() => {
        if (this.popupWindow && !this.popupWindow.closed) {
          this.popupWindow.close();
          this.cleanup();
          resolve({
            success: false,
            message: 'Authentication timeout',
          });
        }
      }, 300000);
    });
  }
  
  /**
   * Clean up popup resources
   */
  cleanup() {
    if (this.popupInterval) {
      clearInterval(this.popupInterval);
      this.popupInterval = null;
    }
    this.popupWindow = null;
  }
  
  /**
   * Generate random state for CSRF protection
   * @returns {string} Random state
   */
  generateState() {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }
  
  /**
   * Handle OAuth redirect (for redirect flow instead of popup)
   * @param {string} provider - Provider name
   */
  redirect(provider) {
    const providerConfig = this.providers[provider];
    
    if (!providerConfig || !providerConfig.clientId) {
      console.error(`Invalid provider: ${provider}`);
      return;
    }
    
    // Generate state
    const state = this.generateState();
    localStorage.setItem('oauth_state', state);
    localStorage.setItem('oauth_provider', provider);
    
    // Build authorization URL
    const params = new URLSearchParams({
      client_id: providerConfig.clientId,
      redirect_uri: providerConfig.redirectUri,
      response_type: 'code',
      scope: providerConfig.scope,
      state: state,
    });
    
    // Redirect to OAuth provider
    window.location.href = `${providerConfig.authUrl}?${params.toString()}`;
  }
}

// Create singleton instance
const oauthService = new OAuthService();

export default oauthService;