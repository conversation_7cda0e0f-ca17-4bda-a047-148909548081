import { createRouter, createWebHistory } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import { config } from '@/config';

// Layout components
const AuthLayout = () => import('@/layouts/AuthLayout.vue');
const AppLayout = () => import('@/layouts/AppLayout.vue');

// Auth pages
const Login = () => import('@/views/auth/Login.vue');
const Register = () => import('@/views/auth/Register.vue');
const ForgotPassword = () => import('@/views/auth/ForgotPassword.vue');
const ResetPassword = () => import('@/views/auth/ResetPassword.vue');
const VerifyEmail = () => import('@/views/auth/VerifyEmail.vue');
const OAuthCallback = () => import('@/views/auth/OAuthCallback.vue');

// App pages
const Dashboard = () => import('@/views/Dashboard.vue');
const Profile = () => import('@/views/Profile.vue');
const Settings = () => import('@/views/Settings.vue');

// Error pages
const NotFound = () => import('@/views/errors/NotFound.vue');
const Forbidden = () => import('@/views/errors/Forbidden.vue');

const routes = [
  // Auth routes
  {
    path: '/auth',
    component: AuthLayout,
    redirect: '/login',
    children: [
      {
        path: '/login',
        name: 'Login',
        component: Login,
        meta: {
          title: 'Login',
          requiresGuest: true,
        },
      },
      {
        path: '/register',
        name: 'Register',
        component: Register,
        meta: {
          title: 'Register',
          requiresGuest: true,
        },
      },
      {
        path: '/forgot-password',
        name: 'ForgotPassword',
        component: ForgotPassword,
        meta: {
          title: 'Forgot Password',
          requiresGuest: true,
        },
      },
      {
        path: '/reset-password',
        name: 'ResetPassword',
        component: ResetPassword,
        meta: {
          title: 'Reset Password',
          requiresGuest: true,
        },
      },
      {
        path: '/verify-email',
        name: 'VerifyEmail',
        component: VerifyEmail,
        meta: {
          title: 'Verify Email',
        },
      },
      {
        path: '/auth/callback/:provider',
        name: 'OAuthCallback',
        component: OAuthCallback,
        meta: {
          title: 'Authenticating...',
        },
      },
    ],
  },
  
  // App routes
  {
    path: '/',
    component: AppLayout,
    meta: {
      requiresAuth: true,
    },
    children: [
      {
        path: '',
        redirect: '/dashboard',
      },
      {
        path: '/dashboard',
        name: 'Dashboard',
        component: Dashboard,
        meta: {
          title: 'Dashboard',
          requiresAuth: true,
        },
      },
      {
        path: '/profile',
        name: 'Profile',
        component: Profile,
        meta: {
          title: 'Profile',
          requiresAuth: true,
        },
      },
      {
        path: '/settings',
        name: 'Settings',
        component: Settings,
        meta: {
          title: 'Settings',
          requiresAuth: true,
        },
      },
    ],
  },
  
  // Error routes
  {
    path: '/403',
    name: 'Forbidden',
    component: Forbidden,
    meta: {
      title: 'Access Denied',
    },
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound,
    meta: {
      title: 'Page Not Found',
    },
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else if (to.hash) {
      return {
        el: to.hash,
        behavior: 'smooth',
      };
    } else {
      return { top: 0 };
    }
  },
});

// Navigation guards
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore();
  
  // Initialize auth if not already done
  if (!authStore.isInitialized) {
    await authStore.initialize();
  }
  
  // Set page title
  document.title = to.meta.title 
    ? `${to.meta.title} - ${config.app.name}` 
    : config.app.name;
  
  // Check authentication requirements
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth);
  const requiresGuest = to.matched.some(record => record.meta.requiresGuest);
  const requiresVerification = to.matched.some(record => record.meta.requiresVerification);
  const requiredRole = to.meta.role;
  const requiredPermission = to.meta.permission;
  
  // Handle guest-only routes
  if (requiresGuest && authStore.isAuthenticated) {
    next('/dashboard');
    return;
  }
  
  // Handle auth-required routes
  if (requiresAuth && !authStore.isAuthenticated) {
    next({
      path: '/login',
      query: { redirect: to.fullPath },
    });
    return;
  }
  
  // Check email verification
  if (requiresVerification && !authStore.isEmailVerified) {
    next('/verify-email');
    return;
  }
  
  // Check role requirements
  if (requiredRole && !authStore.hasRole(requiredRole)) {
    next('/403');
    return;
  }
  
  // Check permission requirements
  if (requiredPermission && !authStore.hasPermission(requiredPermission)) {
    next('/403');
    return;
  }
  
  next();
});

// Handle router errors
router.onError((error) => {
  console.error('Router error:', error);
  
  // Handle chunk load errors (code splitting)
  if (error.message.includes('Failed to fetch dynamically imported module')) {
    window.location.reload();
  }
});

// Progress bar for route transitions
let progressBarTimeout;

router.beforeResolve((to, from, next) => {
  // Start progress bar
  if (to.path !== from.path) {
    progressBarTimeout = setTimeout(() => {
      // Show progress bar (implement your progress bar logic here)
      console.log('Loading...');
    }, 200);
  }
  next();
});

router.afterEach(() => {
  // Stop progress bar
  clearTimeout(progressBarTimeout);
  // Hide progress bar (implement your progress bar logic here)
});

export default router;