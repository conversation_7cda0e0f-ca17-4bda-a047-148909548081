import { createApp, h } from 'vue';
import ErrorToast from '@/components/ErrorToast.vue';

class ErrorHandler {
  constructor() {
    this.toastContainer = null;
    this.toasts = [];
    this.initContainer();
  }

  initContainer() {
    // Create container for toasts
    this.toastContainer = document.createElement('div');
    this.toastContainer.id = 'toast-container';
    this.toastContainer.className = 'fixed top-4 right-4 z-50 space-y-4';
    document.body.appendChild(this.toastContainer);
  }

  /**
   * Show a toast notification
   * @param {Object} options - Toast options
   * @param {string} options.type - Type of toast (success, error, warning, info)
   * @param {string} options.title - Toast title
   * @param {string} options.message - Toast message
   * @param {string} options.details - Additional details (for debugging)
   * @param {number} options.duration - Duration in milliseconds (0 for no auto-dismiss)
   * @param {Array} options.actions - Action buttons
   * @returns {Function} Close function
   */
  showToast(options = {}) {
    const {
      type = 'info',
      title = '',
      message = '',
      details = '',
      duration = 5000,
      actions = []
    } = options;

    // Create a container for this toast
    const toastEl = document.createElement('div');
    this.toastContainer.appendChild(toastEl);

    // Create Vue app for the toast
    const app = createApp({
      render() {
        return h(ErrorToast, {
          type,
          title,
          message,
          details,
          duration,
          actions,
          onClose: () => {
            app.unmount();
            toastEl.remove();
          }
        });
      }
    });

    // Mount the toast
    app.mount(toastEl);

    // Return close function
    return () => {
      app.unmount();
      toastEl.remove();
    };
  }

  /**
   * Show success toast
   */
  success(message, title = '操作成功', options = {}) {
    return this.showToast({
      type: 'success',
      title,
      message,
      ...options
    });
  }

  /**
   * Show error toast
   */
  error(message, title = '操作失败', options = {}) {
    // Parse error message if it's an object
    let errorMessage = message;
    let errorDetails = '';

    if (typeof message === 'object') {
      if (message.message) {
        errorMessage = message.message;
      } else if (message.msg) {
        errorMessage = message.msg;
      } else if (message.error) {
        errorMessage = message.error;
      } else {
        errorMessage = '发生未知错误';
      }

      // Add details for debugging
      if (process.env.NODE_ENV === 'development') {
        errorDetails = JSON.stringify(message, null, 2);
      }
    }

    return this.showToast({
      type: 'error',
      title,
      message: errorMessage,
      details: errorDetails,
      duration: 8000, // Longer duration for errors
      ...options
    });
  }

  /**
   * Show warning toast
   */
  warning(message, title = '警告', options = {}) {
    return this.showToast({
      type: 'warning',
      title,
      message,
      ...options
    });
  }

  /**
   * Show info toast
   */
  info(message, title = '提示', options = {}) {
    return this.showToast({
      type: 'info',
      title,
      message,
      ...options
    });
  }

  /**
   * Handle API errors
   */
  handleApiError(error) {
    console.error('API Error:', error);

    // Extract error information
    let title = '请求失败';
    let message = '发生了一个错误，请稍后重试';
    let actions = [];

    if (error.status === 401) {
      title = '认证失败';
      message = error.message || '您的会话已过期，请重新登录';
      actions = [
        {
          text: '重新登录',
          primary: true,
          handler: () => {
            window.location.href = '/login';
          }
        }
      ];
    } else if (error.status === 403) {
      title = '权限不足';
      message = error.message || '您没有权限执行此操作';
    } else if (error.status === 404) {
      title = '资源不存在';
      message = error.message || '请求的资源不存在';
    } else if (error.status === 422) {
      title = '验证失败';
      message = error.message || '提交的数据验证失败';
    } else if (error.status >= 500) {
      title = '服务器错误';
      message = error.message || '服务器发生错误，请稍后重试';
      actions = [
        {
          text: '重试',
          primary: true,
          handler: () => {
            window.location.reload();
          }
        }
      ];
    } else if (error.code === 'NETWORK_ERROR') {
      title = '网络错误';
      message = '无法连接到服务器，请检查您的网络连接';
    } else {
      // Use error message if available
      if (error.message) {
        message = error.message;
      }
    }

    // Show error toast
    this.error(message, title, {
      actions,
      details: process.env.NODE_ENV === 'development' ? JSON.stringify(error, null, 2) : ''
    });
  }

  /**
   * Handle form validation errors
   */
  handleValidationError(errors) {
    const errorMessages = [];
    
    if (Array.isArray(errors)) {
      errorMessages.push(...errors);
    } else if (typeof errors === 'object') {
      for (const field in errors) {
        const fieldErrors = errors[field];
        if (Array.isArray(fieldErrors)) {
          errorMessages.push(...fieldErrors);
        } else {
          errorMessages.push(fieldErrors);
        }
      }
    } else {
      errorMessages.push(errors);
    }

    const message = errorMessages.join('\n');
    this.error(message, '验证失败', { duration: 10000 });
  }

  /**
   * Install as Vue plugin
   */
  install(app) {
    // Add to global properties
    app.config.globalProperties.$toast = this;
    app.config.globalProperties.$error = this.error.bind(this);
    app.config.globalProperties.$success = this.success.bind(this);
    app.config.globalProperties.$warning = this.warning.bind(this);
    app.config.globalProperties.$info = this.info.bind(this);

    // Provide for composition API
    app.provide('toast', this);

    // Global error handler
    app.config.errorHandler = (err, instance, info) => {
      console.error('Vue Error:', err, info);
      this.error(err.message || '应用程序发生错误', '错误', {
        details: `${err.stack}\n\nComponent: ${info}`
      });
    };

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      console.error('Unhandled Promise Rejection:', event.reason);
      this.handleApiError(event.reason);
      event.preventDefault();
    });
  }
}

// Create singleton instance
const errorHandler = new ErrorHandler();

// Export for use in non-Vue contexts
export const toast = errorHandler;

// Export plugin
export default {
  install: (app) => errorHandler.install(app)
};