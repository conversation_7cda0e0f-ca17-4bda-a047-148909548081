import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import authService from '@/services/auth';
import oauthService from '@/services/oauth';
import { userStorage } from '@/utils/storage';
import { mockApi } from '@/services/api';
import router from '@/router';

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref(null);
  const isLoading = ref(false);
  const isInitialized = ref(false);
  const error = ref(null);
  const sessionTimeout = ref(null);
  
  // Permission related state
  const permissions = ref(new Set());
  const menuTree = ref([]);
  const buttonPermissions = ref(new Set());
  const permissionsLoading = ref(false);
  
  // Computed
  const isAuthenticated = computed(() => !!user.value);
  const userRole = computed(() => user.value?.role || 'guest');
  const userPermissions = computed(() => Array.from(permissions.value));
  const isEmailVerified = computed(() => user.value?.emailVerified || false);
  const hasMFA = computed(() => user.value?.mfaEnabled || false);
  const hasAnyPermission = computed(() => permissions.value.size > 0);
  
  // Actions
  /**
   * Initialize authentication
   */
  async function initialize() {
    if (isInitialized.value) return;
    
    isLoading.value = true;
    error.value = null;
    
    try {
      const result = await authService.initialize();
      if (result.success) {
        user.value = result.user;
        startSessionTimer();
        
        // 初始化时获取用户权限
        await loadUserPermissions(result.user.id);
      }
    } catch (err) {
      console.error('Auth initialization error:', err);
      error.value = err.message;
    } finally {
      isLoading.value = false;
      isInitialized.value = true;
    }
  }
  
  /**
   * User login
   */
  async function login(credentials, rememberMe = false) {
    isLoading.value = true;
    error.value = null;
    
    try {
      const result = await authService.login(credentials, rememberMe);
      
      if (result.success) {
        user.value = result.user;
        startSessionTimer();
        
        // 登录成功后获取用户权限
        await loadUserPermissions(result.user.id);
        
        // Redirect to intended page or dashboard
        const redirect = router.currentRoute.value.query.redirect || '/dashboard';
        await router.push(redirect);
      } else {
        error.value = result.message;
      }
      
      return result;
    } catch (err) {
      error.value = err.message || 'Login failed';
      return { success: false, message: error.value };
    } finally {
      isLoading.value = false;
    }
  }
  
  /**
   * OAuth login
   */
  async function oauthLogin(provider) {
    isLoading.value = true;
    error.value = null;
    
    try {
      const result = await oauthService.login(provider);
      
      if (result.success) {
        user.value = result.user;
        startSessionTimer();
        
        // OAuth登录成功后获取用户权限
        await loadUserPermissions(result.user.id);
        
        // Redirect to dashboard or profile setup for new users
        const redirect = result.isNewUser ? '/profile/setup' : '/dashboard';
        await router.push(redirect);
      } else {
        error.value = result.message;
      }
      
      return result;
    } catch (err) {
      error.value = err.message || 'OAuth login failed';
      return { success: false, message: error.value };
    } finally {
      isLoading.value = false;
    }
  }
  
  /**
   * User registration
   */
  async function register(userData) {
    isLoading.value = true;
    error.value = null;
    
    try {
      const result = await authService.register(userData);
      
      if (result.success) {
        if (!result.requiresVerification) {
          user.value = result.user;
          startSessionTimer();
          await router.push('/dashboard');
        } else {
          await router.push('/verify-email');
        }
      } else {
        error.value = result.message;
      }
      
      return result;
    } catch (err) {
      error.value = err.message || 'Registration failed';
      return { success: false, message: error.value };
    } finally {
      isLoading.value = false;
    }
  }
  
  /**
   * User logout
   */
  async function logout() {
    isLoading.value = true;
    error.value = null;
    
    try {
      await authService.logout();
      user.value = null;
      clearPermissions(); // 清除权限数据
      stopSessionTimer();
      await router.push('/login');
    } catch (err) {
      console.error('Logout error:', err);
    } finally {
      isLoading.value = false;
    }
  }
  
  /**
   * Forgot password
   */
  async function forgotPassword(email) {
    isLoading.value = true;
    error.value = null;
    
    try {
      const result = await authService.forgotPassword(email);
      
      if (!result.success) {
        error.value = result.message;
      }
      
      return result;
    } catch (err) {
      error.value = err.message || 'Failed to send reset email';
      return { success: false, message: error.value };
    } finally {
      isLoading.value = false;
    }
  }
  
  /**
   * Reset password
   */
  async function resetPassword(token, password) {
    isLoading.value = true;
    error.value = null;
    
    try {
      const result = await authService.resetPassword(token, password);
      
      if (result.success) {
        await router.push('/login');
      } else {
        error.value = result.message;
      }
      
      return result;
    } catch (err) {
      error.value = err.message || 'Failed to reset password';
      return { success: false, message: error.value };
    } finally {
      isLoading.value = false;
    }
  }
  
  /**
   * Verify email
   */
  async function verifyEmail(token) {
    isLoading.value = true;
    error.value = null;
    
    try {
      const result = await authService.verifyEmail(token);
      
      if (result.success) {
        user.value = result.user;
        startSessionTimer();
        await router.push('/dashboard');
      } else {
        error.value = result.message;
      }
      
      return result;
    } catch (err) {
      error.value = err.message || 'Email verification failed';
      return { success: false, message: error.value };
    } finally {
      isLoading.value = false;
    }
  }
  
  /**
   * Resend verification email
   */
  async function resendVerification(email) {
    isLoading.value = true;
    error.value = null;
    
    try {
      const result = await authService.resendVerification(email);
      
      if (!result.success) {
        error.value = result.message;
      }
      
      return result;
    } catch (err) {
      error.value = err.message || 'Failed to resend verification email';
      return { success: false, message: error.value };
    } finally {
      isLoading.value = false;
    }
  }
  
  /**
   * Update user profile
   */
  async function updateProfile(updates) {
    isLoading.value = true;
    error.value = null;
    
    try {
      const response = await authService.updateProfile(updates);
      
      if (response.success) {
        user.value = { ...user.value, ...updates };
        userStorage.setUser(user.value);
      } else {
        error.value = response.message;
      }
      
      return response;
    } catch (err) {
      error.value = err.message || 'Failed to update profile';
      return { success: false, message: error.value };
    } finally {
      isLoading.value = false;
    }
  }
  
  /**
   * Check permission
   */
  function hasPermission(permission) {
    if (!user.value) return false;
    if (user.value.role === 'admin') return true;
    return userPermissions.value.includes(permission);
  }
  
  /**
   * Check role
   */
  function hasRole(role) {
    if (!user.value) return false;
    return user.value.role === role || user.value.roles?.includes(role);
  }
  
  /**
   * Start session timer
   */
  function startSessionTimer() {
    stopSessionTimer();
    
    const timeout = user.value?.sessionTimeout || 3600000; // 1 hour default
    
    sessionTimeout.value = setTimeout(() => {
      // Show session expiry warning
      const continueSession = confirm('Your session is about to expire. Do you want to continue?');
      
      if (continueSession) {
        // Refresh token
        authService.refreshToken();
        startSessionTimer();
      } else {
        logout();
      }
    }, timeout - 60000); // Show warning 1 minute before expiry
  }
  
  /**
   * Stop session timer
   */
  function stopSessionTimer() {
    if (sessionTimeout.value) {
      clearTimeout(sessionTimeout.value);
      sessionTimeout.value = null;
    }
  }
  
  /**
   * Load user permissions
   */
  async function loadUserPermissions(userId) {
    if (!userId) return;

    permissionsLoading.value = true;
    try {
      // 获取用户菜单权限
      const menuResponse = await mockApi.getUserMenus(userId);
      if (menuResponse.data) {
        menuTree.value = menuResponse.data || [];
        
        // 提取权限标识
        const extractPermissions = (menus) => {
          const perms = new Set();
          menus.forEach(menu => {
            if (menu.permission) {
              perms.add(menu.permission);
            }
            if (menu.children && menu.children.length > 0) {
              const childPerms = extractPermissions(menu.children);
              childPerms.forEach(perm => perms.add(perm));
            }
          });
          return perms;
        };
        
        permissions.value = extractPermissions(menuTree.value);
      }

      // 获取用户按钮权限
      try {
        const buttonResponse = await mockApi.getUserButtonPermissions(userId);
        if (buttonResponse.data) {
          buttonPermissions.value = new Set(buttonResponse.data || []);
        }
      } catch (buttonError) {
        console.warn('获取按钮权限失败:', buttonError);
      }
    } catch (error) {
      console.error('获取用户权限失败:', error);
    } finally {
      permissionsLoading.value = false;
    }
  }

  /**
   * Check if user has permission
   */
  function hasPermission(permission) {
    if (!permission) return true;
    return permissions.value.has(permission);
  }

  /**
   * Check if user has any of the permissions
   */
  function hasAnyPermissions(permissionList) {
    if (!permissionList || permissionList.length === 0) return true;
    return permissionList.some(permission => permissions.value.has(permission));
  }

  /**
   * Check if user has all permissions
   */
  function hasAllPermissions(permissionList) {
    if (!permissionList || permissionList.length === 0) return true;
    return permissionList.every(permission => permissions.value.has(permission));
  }

  /**
   * Check button permission
   */
  function hasButtonPermission(buttonPermission) {
    if (!buttonPermission) return true;
    return buttonPermissions.value.has(buttonPermission);
  }

  /**
   * Filter menus based on permissions
   */
  function filterMenus(menus) {
    return menus.filter(menu => {
      // 如果菜单有权限要求，检查用户是否有该权限
      if (menu.permission && !hasPermission(menu.permission)) {
        return false;
      }
      
      // 递归过滤子菜单
      if (menu.children && menu.children.length > 0) {
        menu.children = filterMenus(menu.children);
      }
      
      return true;
    });
  }

  /**
   * Get accessible menu tree
   */
  function getAccessibleMenuTree() {
    return filterMenus([...menuTree.value]);
  }

  /**
   * Clear permissions
   */
  function clearPermissions() {
    permissions.value.clear();
    menuTree.value = [];
    buttonPermissions.value.clear();
  }

  /**
   * Refresh user permissions
   */
  async function refreshPermissions() {
    if (user.value?.id) {
      clearPermissions();
      await loadUserPermissions(user.value.id);
    }
  }

  /**
   * Clear error
   */
  function clearError() {
    error.value = null;
  }
  
  return {
    // State
    user,
    isLoading,
    isInitialized,
    error,
    permissions,
    menuTree,
    buttonPermissions,
    permissionsLoading,
    
    // Computed
    isAuthenticated,
    userRole,
    userPermissions,
    isEmailVerified,
    hasMFA,
    hasAnyPermission,
    
    // Actions
    initialize,
    login,
    oauthLogin,
    register,
    logout,
    forgotPassword,
    resetPassword,
    verifyEmail,
    resendVerification,
    updateProfile,
    loadUserPermissions,
    hasPermission,
    hasAnyPermissions,
    hasAllPermissions,
    hasButtonPermission,
    hasRole,
    filterMenus,
    getAccessibleMenuTree,
    clearPermissions,
    refreshPermissions,
    clearError,
  };
});