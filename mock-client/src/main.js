import { createApp } from 'vue';
import { createPinia } from 'pinia';
import App from './App.vue';
import router from './router';
import errorHandler from './plugins/errorHandler';
import './style.css';

// Create Vue app
const app = createApp(App);

// Create Pinia store
const pinia = createPinia();

// Use plugins
app.use(pinia);
app.use(router);
app.use(errorHandler); // Add error handler plugin

// Mount app
app.mount('#app');
