<template>
  <Transition name="toast">
    <div v-if="visible" 
         :class="[
           'fixed top-4 right-4 z-50 max-w-md w-full',
           'bg-white rounded-lg shadow-xl border-l-4',
           typeClasses[type].border
         ]">
      <div class="p-4">
        <div class="flex items-start">
          <!-- Icon -->
          <div :class="['flex-shrink-0', typeClasses[type].icon]">
            <component :is="getIcon(type)" class="h-6 w-6" />
          </div>
          
          <!-- Content -->
          <div class="ml-3 flex-1">
            <h3 :class="['text-sm font-medium', typeClasses[type].title]">
              {{ title || getDefaultTitle(type) }}
            </h3>
            <p v-if="message" class="mt-1 text-sm text-gray-600">
              {{ message }}
            </p>
            
            <!-- Details (for debugging) -->
            <details v-if="details && showDetails" class="mt-2">
              <summary class="text-xs text-gray-500 cursor-pointer hover:text-gray-700">
                详细信息
              </summary>
              <pre class="mt-1 text-xs text-gray-500 overflow-auto max-h-32">{{ details }}</pre>
            </details>
          </div>
          
          <!-- Close button -->
          <button @click="close" 
                  class="ml-4 flex-shrink-0 inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
            <span class="sr-only">关闭</span>
            <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" 
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" 
                    clip-rule="evenodd" />
            </svg>
          </button>
        </div>
        
        <!-- Actions -->
        <div v-if="actions && actions.length > 0" class="mt-4 flex space-x-3">
          <button v-for="action in actions" 
                  :key="action.text"
                  @click="handleAction(action)"
                  :class="[
                    'text-sm font-medium rounded-md px-3 py-2 transition-colors',
                    action.primary 
                      ? 'bg-indigo-600 text-white hover:bg-indigo-700' 
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  ]">
            {{ action.text }}
          </button>
        </div>
      </div>
      
      <!-- Progress bar for auto-dismiss -->
      <div v-if="duration > 0" class="h-1 bg-gray-100">
        <div :class="['h-full transition-all duration-300', typeClasses[type].progress]"
             :style="{ width: `${progress}%` }"></div>
      </div>
    </div>
  </Transition>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';

const props = defineProps({
  type: {
    type: String,
    default: 'error',
    validator: (value) => ['success', 'error', 'warning', 'info'].includes(value)
  },
  title: String,
  message: String,
  details: String,
  duration: {
    type: Number,
    default: 5000 // 5 seconds, 0 for no auto-dismiss
  },
  showDetails: {
    type: Boolean,
    default: process.env.NODE_ENV === 'development'
  },
  actions: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['close', 'action']);

const visible = ref(true);
const progress = ref(100);
let dismissTimer = null;
let progressTimer = null;

const typeClasses = {
  success: {
    border: 'border-green-500',
    icon: 'text-green-500',
    title: 'text-green-800',
    progress: 'bg-green-500'
  },
  error: {
    border: 'border-red-500',
    icon: 'text-red-500',
    title: 'text-red-800',
    progress: 'bg-red-500'
  },
  warning: {
    border: 'border-yellow-500',
    icon: 'text-yellow-500',
    title: 'text-yellow-800',
    progress: 'bg-yellow-500'
  },
  info: {
    border: 'border-blue-500',
    icon: 'text-blue-500',
    title: 'text-blue-800',
    progress: 'bg-blue-500'
  }
};

const getIcon = (type) => {
  const icons = {
    success: 'SuccessIcon',
    error: 'ErrorIcon',
    warning: 'WarningIcon',
    info: 'InfoIcon'
  };
  return icons[type] || 'InfoIcon';
};

const getDefaultTitle = (type) => {
  const titles = {
    success: '操作成功',
    error: '操作失败',
    warning: '警告',
    info: '提示'
  };
  return titles[type] || '提示';
};

const close = () => {
  visible.value = false;
  clearTimers();
  setTimeout(() => {
    emit('close');
  }, 300); // Wait for transition
};

const handleAction = (action) => {
  if (action.handler) {
    action.handler();
  }
  emit('action', action);
  if (action.closeOnClick !== false) {
    close();
  }
};

const startAutoDismiss = () => {
  if (props.duration <= 0) return;
  
  const interval = 100; // Update every 100ms
  const steps = props.duration / interval;
  const decrement = 100 / steps;
  
  progressTimer = setInterval(() => {
    progress.value -= decrement;
    if (progress.value <= 0) {
      progress.value = 0;
      clearInterval(progressTimer);
    }
  }, interval);
  
  dismissTimer = setTimeout(() => {
    close();
  }, props.duration);
};

const clearTimers = () => {
  if (dismissTimer) {
    clearTimeout(dismissTimer);
    dismissTimer = null;
  }
  if (progressTimer) {
    clearInterval(progressTimer);
    progressTimer = null;
  }
};

onMounted(() => {
  startAutoDismiss();
});

onUnmounted(() => {
  clearTimers();
});

// Icon components
const SuccessIcon = {
  template: `
    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  `
};

const ErrorIcon = {
  template: `
    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
            d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  `
};

const WarningIcon = {
  template: `
    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
    </svg>
  `
};

const InfoIcon = {
  template: `
    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  `
};
</script>

<style scoped>
.toast-enter-active,
.toast-leave-active {
  transition: all 0.3s ease;
}

.toast-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.toast-leave-to {
  transform: translateX(100%);
  opacity: 0;
}
</style>