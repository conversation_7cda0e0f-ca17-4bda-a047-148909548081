<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- Logo and Title -->
      <div class="text-center">
        <div class="flex justify-center">
          <div class="w-20 h-20 bg-primary-600 rounded-full flex items-center justify-center">
            <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
        </div>
        <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
          {{ verifying ? 'Verifying your email...' : 'Verify your email' }}
        </h2>
        <p class="mt-2 text-sm text-gray-600">
          {{ verifying ? 'Please wait while we verify your email address.' : 'Check your email for verification link.' }}
        </p>
      </div>

      <!-- Loading State -->
      <div v-if="verifying" class="flex justify-center">
        <div class="spinner"></div>
      </div>

      <!-- Success Message -->
      <transition name="slide-down">
        <div v-if="success" class="rounded-md bg-green-50 p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" 
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" 
                  clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-green-800">
                Email verified successfully!
              </h3>
              <div class="mt-2 text-sm text-green-700">
                <p>Your email has been verified. You can now access all features.</p>
              </div>
            </div>
          </div>
        </div>
      </transition>

      <!-- Error Message -->
      <transition name="slide-down">
        <div v-if="error && !verifying" class="rounded-md bg-red-50 p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" 
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" 
                  clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">
                {{ error }}
              </h3>
            </div>
          </div>
        </div>
      </transition>

      <!-- Actions -->
      <div v-if="!verifying" class="space-y-4">
        <div v-if="!success">
          <p class="text-center text-sm text-gray-600 mb-4">
            Didn't receive the email? Check your spam folder or request a new one.
          </p>
          <button
            @click="handleResend"
            :disabled="isLoading || resendCooldown > 0"
            :class="[
              'group relative w-full flex justify-center py-2 px-4 border',
              'text-sm font-medium rounded-md',
              'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500',
              'transition-colors duration-200',
              isLoading || resendCooldown > 0
                ? 'bg-gray-100 text-gray-400 border-gray-300 cursor-not-allowed'
                : 'bg-white text-primary-600 border-primary-600 hover:bg-primary-50'
            ]"
          >
            {{ resendCooldown > 0 ? `Resend in ${resendCooldown}s` : 'Resend verification email' }}
          </button>
        </div>

        <div class="text-center">
          <router-link 
            :to="success ? '/dashboard' : '/login'" 
            class="text-sm font-medium text-primary-600 hover:text-primary-500"
          >
            {{ success ? 'Go to dashboard' : 'Back to login' }}
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { useRoute } from 'vue-router';
import { useAuthStore } from '@/stores/auth';

const route = useRoute();
const authStore = useAuthStore();

// State
const verifying = ref(false);
const isLoading = ref(false);
const success = ref(false);
const error = ref(null);
const resendCooldown = ref(0);
let resendInterval = null;

// Verify email with token
const verifyEmail = async (token) => {
  verifying.value = true;
  error.value = null;
  
  try {
    const result = await authStore.verifyEmail(token);
    
    if (result.success) {
      success.value = true;
    } else {
      error.value = result.message || 'Verification failed. The link may be expired or invalid.';
    }
  } catch (err) {
    error.value = 'An error occurred during verification. Please try again.';
    console.error('Verification error:', err);
  } finally {
    verifying.value = false;
  }
};

// Handle resend
const handleResend = async () => {
  if (resendCooldown.value > 0) return;
  
  isLoading.value = true;
  error.value = null;
  
  try {
    const email = authStore.user?.email || route.query.email;
    
    if (!email) {
      error.value = 'Email address not found. Please login and try again.';
      return;
    }
    
    const result = await authStore.resendVerification(email);
    
    if (result.success) {
      startResendCooldown();
    } else {
      error.value = result.message;
    }
  } catch (err) {
    error.value = 'Failed to resend verification email. Please try again.';
    console.error('Resend error:', err);
  } finally {
    isLoading.value = false;
  }
};

// Start resend cooldown
const startResendCooldown = () => {
  resendCooldown.value = 60; // 60 seconds cooldown
  
  if (resendInterval) {
    clearInterval(resendInterval);
  }
  
  resendInterval = setInterval(() => {
    resendCooldown.value--;
    if (resendCooldown.value <= 0) {
      clearInterval(resendInterval);
      resendInterval = null;
    }
  }, 1000);
};

// Check for verification token
onMounted(() => {
  const token = route.query.token;
  if (token) {
    verifyEmail(token);
  }
});

// Cleanup
onUnmounted(() => {
  if (resendInterval) {
    clearInterval(resendInterval);
  }
});
</script>

<style scoped>
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
}

.slide-down-enter-from {
  transform: translateY(-10px);
  opacity: 0;
}

.slide-down-leave-to {
  transform: translateY(-10px);
  opacity: 0;
}
</style>