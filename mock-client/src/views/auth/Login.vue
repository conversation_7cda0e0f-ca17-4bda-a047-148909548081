<template>
  <div class="min-h-screen flex items-center justify-center relative overflow-hidden">
    <!-- 动态背景 -->
    <div class="absolute inset-0 bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 animate-gradient-shift"></div>
    
    <!-- 装饰性元素 -->
    <div class="absolute inset-0 overflow-hidden">
      <div class="absolute -top-40 -right-40 w-80 h-80 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
      <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-yellow-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
      <div class="absolute top-40 left-40 w-80 h-80 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
    </div>

    <div class="relative z-10 max-w-md w-full mx-4">
      <!-- 登录卡片 -->
      <div class="bg-white/95 backdrop-blur-lg rounded-3xl shadow-2xl p-8 space-y-6 animate-fade-in-up">
        <!-- Logo 和标题 -->
        <div class="text-center space-y-2">
          <div class="flex justify-center mb-4">
            <div class="relative">
              <div class="w-24 h-24 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center transform rotate-3 transition-transform hover:rotate-6">
                <svg class="w-14 h-14 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                    d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <div class="absolute -bottom-1 -right-1 w-6 h-6 bg-green-400 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
              </div>
            </div>
          </div>
          <h2 class="text-3xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
            欢迎回来
          </h2>
          <p class="text-gray-600">
            登录您的账户以继续
          </p>
        </div>

        <!-- 登录表单 -->
        <form @submit.prevent="handleSubmit" class="space-y-5">
          <!-- 用户名/邮箱输入框 -->
          <div class="space-y-2">
            <label for="username" class="text-sm font-medium text-gray-700 flex items-center">
              <svg class="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              用户名或邮箱
            </label>
            <div class="relative">
              <input
                id="username"
                v-model="formData.username"
                type="text"
                required
                :class="[
                  'w-full px-4 py-3 rounded-xl border-2 transition-all duration-200',
                  'placeholder-gray-400 text-gray-900',
                  'focus:outline-none focus:ring-4 focus:ring-purple-100 focus:border-purple-500',
                  'hover:border-purple-300',
                  errors.username ? 'border-red-300 bg-red-50' : 'border-gray-200 bg-white'
                ]"
                placeholder="请输入用户名或邮箱地址"
                @blur="validateField('username')"
                @input="clearFieldError('username')"
              />
              <transition name="fade">
                <div v-if="formData.username && !errors.username" class="absolute right-3 top-1/2 -translate-y-1/2">
                  <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                  </svg>
                </div>
              </transition>
            </div>
            <transition name="slide-down">
              <p v-if="errors.username" class="text-sm text-red-600 mt-1">
                <span class="flex items-center">
                  <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                  </svg>
                  {{ errors.username }}
                </span>
              </p>
            </transition>
          </div>

          <!-- 密码输入框 -->
          <div class="space-y-2">
            <label for="password" class="text-sm font-medium text-gray-700 flex items-center">
              <svg class="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
              密码
            </label>
            <div class="relative">
              <input
                id="password"
                v-model="formData.password"
                :type="showPassword ? 'text' : 'password'"
                required
                :class="[
                  'w-full px-4 py-3 pr-12 rounded-xl border-2 transition-all duration-200',
                  'placeholder-gray-400 text-gray-900',
                  'focus:outline-none focus:ring-4 focus:ring-purple-100 focus:border-purple-500',
                  'hover:border-purple-300',
                  errors.password ? 'border-red-300 bg-red-50' : 'border-gray-200 bg-white'
                ]"
                placeholder="请输入密码"
                @blur="validateField('password')"
                @input="clearFieldError('password')"
              />
              <button
                type="button"
                @click="showPassword = !showPassword"
                class="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 focus:outline-none"
              >
                <svg v-if="!showPassword" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                <svg v-else class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                    d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                </svg>
              </button>
            </div>
            <transition name="slide-down">
              <p v-if="errors.password" class="text-sm text-red-600 mt-1">
                <span class="flex items-center">
                  <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                  </svg>
                  {{ errors.password }}
                </span>
              </p>
            </transition>
          </div>

          <!-- 记住我和忘记密码 -->
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <input
                id="remember-me"
                v-model="formData.rememberMe"
                type="checkbox"
                class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded transition-colors"
              />
              <label for="remember-me" class="ml-2 block text-sm text-gray-700">
                记住我
              </label>
            </div>

            <router-link to="/forgot-password" class="text-sm font-medium text-purple-600 hover:text-purple-500 transition-colors">
              忘记密码？
            </router-link>
          </div>

          <!-- 登录按钮 -->
          <button
            type="submit"
            :disabled="isLoading || !isFormValid"
            :class="[
              'w-full py-3 px-4 rounded-xl font-medium text-white transition-all duration-200',
              'transform hover:scale-[1.02] active:scale-[0.98]',
              'focus:outline-none focus:ring-4 focus:ring-purple-200',
              isLoading || !isFormValid
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 shadow-lg hover:shadow-xl'
            ]"
          >
            <span v-if="!isLoading" class="flex items-center justify-center">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
              </svg>
              登录
            </span>
            <span v-else class="flex items-center justify-center">
              <div class="spinner-sm mr-2"></div>
              登录中...
            </span>
          </button>

          <!-- 第三方登录 -->
          <div v-if="config.features.enableSocialLogin">
            <div class="relative my-6">
              <div class="absolute inset-0 flex items-center">
                <div class="w-full border-t border-gray-300"></div>
              </div>
              <div class="relative flex justify-center text-sm">
                <span class="px-4 bg-white text-gray-500">或使用以下方式登录</span>
              </div>
            </div>

            <div class="grid grid-cols-2 gap-3">
              <button
                v-for="provider in oauthProviders"
                :key="provider.key"
                type="button"
                @click="handleOAuthLogin(provider.key)"
                :disabled="isLoading"
                :class="[
                  'flex items-center justify-center py-2.5 px-4 rounded-xl border-2 border-gray-200',
                  'bg-white text-gray-700 font-medium transition-all duration-200',
                  'hover:bg-gray-50 hover:border-gray-300 hover:shadow-md transform hover:scale-[1.02]',
                  'focus:outline-none focus:ring-4 focus:ring-gray-100',
                  isLoading ? 'cursor-not-allowed opacity-50' : ''
                ]"
              >
                <component :is="getProviderIcon(provider.key)" class="h-5 w-5 mr-2" />
                <span>{{ getProviderName(provider.key) }}</span>
              </button>
            </div>
          </div>
        </form>

        <!-- 注册链接 -->
        <div class="text-center pt-4 border-t border-gray-200">
          <p class="text-sm text-gray-600">
            还没有账户？
            <span
              @click="goToRegister"
              class="font-medium text-purple-600 hover:text-purple-500 transition-colors cursor-pointer">
              立即注册
            </span>
          </p>
        </div>
      </div>

      <!-- 底部链接 -->
      <div class="mt-6 text-center">
        <p class="text-xs text-white/80">
          登录即表示您同意我们的
          <a href="#" class="underline hover:text-white transition-colors">服务条款</a>
          和
          <a href="#" class="underline hover:text-white transition-colors">隐私政策</a>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, inject } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import { validateEmail, validateUsername } from '@/utils/validation';
import { config } from '@/config';
import oauthService from '@/services/oauth';

// OAuth 提供商图标
import GoogleIcon from '@/components/icons/GoogleIcon.vue';
import FacebookIcon from '@/components/icons/FacebookIcon.vue';
import GithubIcon from '@/components/icons/GithubIcon.vue';
import MicrosoftIcon from '@/components/icons/MicrosoftIcon.vue';

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();
const toast = inject('toast'); // 注入 toast 服务

// 表单数据
const formData = ref({
  username: '',
  password: '',
  rememberMe: false,
});

// 界面状态
const showPassword = ref(false);
const isLoading = ref(false);
const errors = ref({});

// OAuth 提供商
const oauthProviders = computed(() => oauthService.getProviders());

// 表单验证
const isFormValid = computed(() => {
  return formData.value.username &&
         formData.value.password &&
         Object.keys(errors.value).length === 0;
});

// 验证单个字段
const validateField = (field) => {
  switch (field) {
    case 'username':
      if (!formData.value.username) {
        errors.value.username = '请输入用户名或邮箱';
      } else if (formData.value.username.includes('@')) {
        const emailValidation = validateEmail(formData.value.username);
        if (!emailValidation.valid) {
          errors.value.username = '请输入有效的邮箱地址';
        }
      } else {
        const usernameValidation = validateUsername(formData.value.username);
        if (!usernameValidation.valid) {
          errors.value.username = '用户名格式不正确';
        }
      }
      break;
    case 'password':
      if (!formData.value.password) {
        errors.value.password = '请输入密码';
      } else if (formData.value.password.length < 6) {
        errors.value.password = '密码至少需要6个字符';
      }
      break;
  }
};

// 清除字段错误
const clearFieldError = (field) => {
  delete errors.value[field];
};

// 处理表单提交
const handleSubmit = async () => {
  // 验证所有字段
  validateField('username');
  validateField('password');
  
  if (!isFormValid.value) {
    return;
  }
  
  isLoading.value = true;
  
  try {
    const result = await authStore.login(
      {
        username: formData.value.username,
        password: formData.value.password,
      },
      formData.value.rememberMe
    );
    
    if (!result.success) {
      // 使用 toast 显示错误
      const errorMessage = result.message || '登录失败，请检查用户名和密码';
      
      if (toast) {
        toast.error(errorMessage, '登录失败', {
          duration: 6000,
          actions: errorMessage.includes('密码') ? [
            {
              text: '忘记密码？',
              primary: true,
              handler: () => {
                router.push('/forgot-password');
              }
            }
          ] : []
        });
      }
    } else {
      // 显示成功消息
      if (toast) {
        toast.success('登录成功，正在跳转...', '欢迎回来');
      }
    }
  } catch (err) {
    const errorMessage = err.message || '发生意外错误，请稍后重试';
    
    if (toast) {
      toast.error(errorMessage, '登录错误', {
        duration: 8000,
        details: err.stack || JSON.stringify(err)
      });
    }
    
    console.error('登录错误:', err);
  } finally {
    isLoading.value = false;
  }
};

// 处理 OAuth 登录
const handleOAuthLogin = async (provider) => {
  isLoading.value = true;
  
  try {
    const result = await authStore.oauthLogin(provider);
    
    if (!result.success) {
      const errorMessage = result.message || '第三方登录失败';
      
      if (toast) {
        toast.error(errorMessage, `${getProviderName(provider)}登录失败`, {
          duration: 6000
        });
      }
    } else {
      if (toast) {
        toast.success('登录成功，正在跳转...', `${getProviderName(provider)}登录成功`);
      }
    }
  } catch (err) {
    const errorMessage = err.message || '第三方登录失败，请稍后重试';
    
    if (toast) {
      toast.error(errorMessage, '第三方登录错误', {
        duration: 8000,
        details: err.stack || JSON.stringify(err)
      });
    }
    
    console.error('OAuth 登录错误:', err);
  } finally {
    isLoading.value = false;
  }
};

// 获取提供商图标组件
const getProviderIcon = (provider) => {
  const icons = {
    google: GoogleIcon,
    facebook: FacebookIcon,
    github: GithubIcon,
    microsoft: MicrosoftIcon,
  };
  return icons[provider] || 'div';
};

// 获取提供商中文名称
const getProviderName = (provider) => {
  const names = {
    google: '谷歌',
    facebook: '脸书',
    github: 'GitHub',
    microsoft: '微软',
  };
  return names[provider] || provider;
};

// 导航到注册页面
const goToRegister = () => {
  console.log('跳转到注册页面');
  // 首先清除表单错误
  errors.value = {};
  
  // 如果 router.push 失败，使用 window.location 作为后备方案
  router.push('/register')
    .then(() => {
      console.log('导航成功');
    })
    .catch(err => {
      console.error('路由导航失败:', err);
      // 后备直接导航
      window.location.href = '/register';
    });
};

// 检查重定向消息
onMounted(() => {
  if (route.query.message) {
    // 为重定向消息显示 toast
    if (toast) {
      const messageType = route.query.type || 'info';
      if (messageType === 'error') {
        toast.error(route.query.message);
      } else if (messageType === 'warning') {
        toast.warning(route.query.message);
      } else {
        toast.info(route.query.message);
      }
    }
  }
  
  // 自动聚焦用户名字段
  document.getElementById('username')?.focus();
});
</script>

<style scoped>
/* 如需要可添加额外的自定义样式 */
</style>