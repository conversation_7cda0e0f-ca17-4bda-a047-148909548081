<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- Logo and Title -->
      <div class="text-center">
        <div class="flex justify-center">
          <div class="w-20 h-20 bg-primary-600 rounded-full flex items-center justify-center">
            <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                d="M15 7a2 2 0 11-4 0 2 2 0 014 0zm0 0v1a2 2 0 01-2 2H9m4 0a2 2 0 012 2v1m-6-6a2 2 0 012-2h1m4 0V5a2 2 0 012-2h1a2 2 0 012 2v10a2 2 0 01-2 2h-1a2 2 0 01-2-2V5z" />
            </svg>
          </div>
        </div>
        <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
          忘记密码了？
        </h2>
        <p class="mt-2 text-sm text-gray-600">
          别担心！输入您的邮箱地址，我们将发送重置密码的说明。
        </p>
      </div>

      <!-- Success Message -->
      <transition name="slide-down">
        <div v-if="success" class="rounded-md bg-green-50 p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" 
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" 
                  clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-green-800">
                请查收邮件！
              </h3>
              <div class="mt-2 text-sm text-green-700">
                <p>
                  我们已将密码重置说明发送至 {{ formData.email }}。
                  请查看您的收件箱并点击链接重置密码。
                </p>
              </div>
            </div>
          </div>
        </div>
      </transition>

      <!-- Error Alert -->
      <transition name="slide-down">
        <div v-if="error && !success" class="rounded-md bg-red-50 p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" 
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" 
                  clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">
                {{ error }}
              </h3>
            </div>
            <div class="ml-auto pl-3">
              <button type="button" @click="error = null" 
                class="inline-flex rounded-md bg-red-50 p-1.5 text-red-500 hover:bg-red-100">
                <span class="sr-only">Dismiss</span>
                <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" 
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" 
                    clip-rule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </transition>

      <!-- Form -->
      <form v-if="!success" class="mt-8 space-y-6" @submit.prevent="handleSubmit">
        <div>
          <label for="email" class="block text-sm font-medium text-gray-700">
            邮箱地址
          </label>
          <div class="mt-1">
            <input
              id="email"
              v-model="formData.email"
              name="email"
              type="email"
              autocomplete="email"
              required
              :disabled="isLoading"
              :class="[
                'appearance-none block w-full px-3 py-2 border rounded-md shadow-sm',
                'placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm',
                errors.email ? 'border-red-300' : 'border-gray-300',
                isLoading ? 'bg-gray-50' : ''
              ]"
              placeholder="请输入您的邮箱地址"
              @blur="validateField('email')"
              @input="clearFieldError('email')"
              :aria-invalid="!!errors.email"
              :aria-describedby="errors.email ? 'email-error' : undefined"
            />
          </div>
          <p v-if="errors.email" id="email-error" class="mt-2 text-sm text-red-600">
            {{ errors.email }}
          </p>
        </div>

        <div>
          <button
            type="submit"
            :disabled="isLoading || !isFormValid"
            :class="[
              'group relative w-full flex justify-center py-2 px-4 border border-transparent',
              'text-sm font-medium rounded-md text-white',
              'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500',
              'transition-colors duration-200',
              isLoading || !isFormValid
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-primary-600 hover:bg-primary-700'
            ]"
          >
            <span class="absolute left-0 inset-y-0 flex items-center pl-3">
              <svg v-if="!isLoading" class="h-5 w-5 text-primary-500 group-hover:text-primary-400" 
                fill="currentColor" viewBox="0 0 20 20">
                <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
              </svg>
              <div v-else class="spinner spinner-sm"></div>
            </span>
            {{ isLoading ? '发送中...' : '发送重置说明' }}
          </button>
        </div>

        <div class="flex items-center justify-center">
          <router-link to="/login" class="text-sm font-medium text-primary-600 hover:text-primary-500">
            返回登录
          </router-link>
        </div>
      </form>

      <!-- Success Actions -->
      <div v-else class="mt-8 space-y-4">
        <button
          @click="handleResend"
          :disabled="isLoading || resendCooldown > 0"
          :class="[
            'group relative w-full flex justify-center py-2 px-4 border',
            'text-sm font-medium rounded-md',
            'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500',
            'transition-colors duration-200',
            isLoading || resendCooldown > 0
              ? 'bg-gray-100 text-gray-400 border-gray-300 cursor-not-allowed'
              : 'bg-white text-primary-600 border-primary-600 hover:bg-primary-50'
          ]"
        >
          {{ resendCooldown > 0 ? `${resendCooldown}秒后重新发送` : '重新发送邮件' }}
        </button>

        <div class="text-center">
          <router-link to="/login" class="text-sm font-medium text-primary-600 hover:text-primary-500">
            返回登录
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onUnmounted } from 'vue';
import { useAuthStore } from '@/stores/auth';
import { validateEmail } from '@/utils/validation';

const authStore = useAuthStore();

// Form data
const formData = ref({
  email: '',
});

// UI state
const isLoading = ref(false);
const error = ref(null);
const errors = ref({});
const success = ref(false);
const resendCooldown = ref(0);
let resendInterval = null;

// Form validation
const isFormValid = computed(() => {
  return formData.value.email && Object.keys(errors.value).length === 0;
});

// Validate field
const validateField = (field) => {
  if (field === 'email') {
    if (!formData.value.email) {
      errors.value.email = '请输入邮箱地址';
    } else {
      const validation = validateEmail(formData.value.email);
      if (!validation.valid) {
        errors.value.email = validation.error;
      }
    }
  }
};

// Clear field error
const clearFieldError = (field) => {
  delete errors.value[field];
};

// Handle form submission
const handleSubmit = async () => {
  validateField('email');
  
  if (!isFormValid.value) {
    return;
  }
  
  isLoading.value = true;
  error.value = null;
  
  try {
    const result = await authStore.forgotPassword(formData.value.email);
    
    if (result.success) {
      success.value = true;
      startResendCooldown();
    } else {
      error.value = result.message;
    }
  } catch (err) {
    error.value = '发生意外错误，请稍后重试。';
    console.error('Forgot password error:', err);
  } finally {
    isLoading.value = false;
  }
};

// Handle resend
const handleResend = async () => {
  if (resendCooldown.value > 0) return;
  
  isLoading.value = true;
  error.value = null;
  
  try {
    const result = await authStore.forgotPassword(formData.value.email);
    
    if (result.success) {
      startResendCooldown();
    } else {
      error.value = result.message;
      success.value = false;
    }
  } catch (err) {
    error.value = '重新发送邮件失败，请稍后重试。';
    console.error('Resend error:', err);
  } finally {
    isLoading.value = false;
  }
};

// Start resend cooldown
const startResendCooldown = () => {
  resendCooldown.value = 60; // 60 seconds cooldown
  
  if (resendInterval) {
    clearInterval(resendInterval);
  }
  
  resendInterval = setInterval(() => {
    resendCooldown.value--;
    if (resendCooldown.value <= 0) {
      clearInterval(resendInterval);
      resendInterval = null;
    }
  }, 1000);
};

// Cleanup
onUnmounted(() => {
  if (resendInterval) {
    clearInterval(resendInterval);
  }
});
</script>

<style scoped>
/* Slide down animation */
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
}

.slide-down-enter-from {
  transform: translateY(-10px);
  opacity: 0;
}

.slide-down-leave-to {
  transform: translateY(-10px);
  opacity: 0;
}
</style>