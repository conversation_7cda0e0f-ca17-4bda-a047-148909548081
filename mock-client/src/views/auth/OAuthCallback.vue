<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div class="text-center">
        <div class="flex justify-center">
          <div class="w-20 h-20 bg-primary-600 rounded-full flex items-center justify-center">
            <div class="spinner"></div>
          </div>
        </div>
        <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
          Authenticating...
        </h2>
        <p class="mt-2 text-sm text-gray-600">
          Please wait while we complete your sign in.
        </p>
      </div>

      <!-- Error Message -->
      <transition name="slide-down">
        <div v-if="error" class="rounded-md bg-red-50 p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" 
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" 
                  clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">
                {{ error }}
              </h3>
            </div>
          </div>
          <div class="mt-4 text-center">
            <router-link to="/login" class="text-sm font-medium text-primary-600 hover:text-primary-500">
              Back to login
            </router-link>
          </div>
        </div>
      </transition>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import oauthService from '@/services/oauth';

const route = useRoute();
const router = useRouter();

const error = ref(null);

onMounted(async () => {
  const provider = route.params.provider;
  const code = route.query.code;
  const errorParam = route.query.error;
  const state = route.query.state;
  
  // Check for error from OAuth provider
  if (errorParam) {
    error.value = errorParam === 'access_denied' 
      ? 'Access was denied. Please try again.'
      : `Authentication failed: ${errorParam}`;
    return;
  }
  
  // Verify state for CSRF protection
  const savedState = sessionStorage.getItem('oauth_state');
  if (state !== savedState) {
    error.value = 'Invalid state parameter. Please try again.';
    return;
  }
  
  // Check for authorization code
  if (!code) {
    error.value = 'No authorization code received. Please try again.';
    return;
  }
  
  try {
    // Handle OAuth callback
    const result = await oauthService.handleCallback(code, provider);
    
    if (result.success) {
      // Redirect to dashboard or profile setup
      const redirect = result.isNewUser ? '/profile/setup' : '/dashboard';
      router.push(redirect);
    } else {
      error.value = result.message || 'Authentication failed. Please try again.';
    }
  } catch (err) {
    console.error('OAuth callback error:', err);
    error.value = 'An unexpected error occurred. Please try again.';
  }
});
</script>

<style scoped>
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
}

.slide-down-enter-from {
  transform: translateY(-10px);
  opacity: 0;
}

.slide-down-leave-to {
  transform: translateY(-10px);
  opacity: 0;
}
</style>