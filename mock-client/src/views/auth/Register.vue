<template>
  <div class="min-h-screen flex items-center justify-center relative overflow-hidden">
    <!-- 动态背景 -->
    <div class="absolute inset-0 bg-gradient-to-br from-pink-500 via-purple-500 to-indigo-500 animate-gradient-shift"></div>
    
    <!-- 装饰性元素 -->
    <div class="absolute inset-0 overflow-hidden">
      <div class="absolute -top-40 -left-40 w-80 h-80 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
      <div class="absolute -bottom-40 -right-40 w-80 h-80 bg-green-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
      <div class="absolute top-40 right-40 w-80 h-80 bg-yellow-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
    </div>

    <div class="relative z-10 max-w-md w-full mx-4">
      <!-- 注册卡片 -->
      <div class="bg-white/95 backdrop-blur-lg rounded-3xl shadow-2xl p-8 space-y-6 animate-fade-in-up">
        <!-- Logo 和标题 -->
        <div class="text-center space-y-2">
          <div class="flex justify-center mb-4">
            <div class="relative">
              <div class="w-24 h-24 bg-gradient-to-br from-pink-500 to-indigo-600 rounded-2xl flex items-center justify-center transform -rotate-3 transition-transform hover:-rotate-6">
                <svg class="w-14 h-14 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                    d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                </svg>
              </div>
              <div class="absolute -bottom-1 -right-1 w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              </div>
            </div>
          </div>
          <h2 class="text-3xl font-bold bg-gradient-to-r from-pink-600 to-indigo-600 bg-clip-text text-transparent">
            创建新账户
          </h2>
          <p class="text-gray-600">
            加入我们，开启精彩之旅
          </p>
        </div>

        <!-- 错误提示 -->
        <transition name="slide-down">
          <div v-if="error" class="rounded-xl bg-red-50 border border-red-200 p-4">
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" 
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" 
                    clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3 flex-1">
                <p class="text-sm font-medium text-red-800">
                  {{ error }}
                </p>
              </div>
              <button type="button" @click="error = null" 
                class="ml-auto flex-shrink-0 inline-flex text-red-400 hover:text-red-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 rounded-lg p-1.5">
                <svg class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" 
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" 
                    clip-rule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </transition>

        <!-- 注册表单 -->
        <form @submit.prevent="handleSubmit" class="space-y-4">
          <!-- 用户名输入框 -->
          <div class="space-y-1">
            <label for="username" class="text-sm font-medium text-gray-700 flex items-center">
              <svg class="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              用户名
            </label>
            <div class="relative">
              <input
                id="username"
                v-model="formData.username"
                type="text"
                required
                :class="[
                  'w-full px-4 py-3 rounded-xl border-2 transition-all duration-200',
                  'placeholder-gray-400 text-gray-900',
                  'focus:outline-none focus:ring-4 focus:ring-indigo-100 focus:border-indigo-500',
                  'hover:border-indigo-300',
                  errors.username ? 'border-red-300 bg-red-50' : 'border-gray-200 bg-white'
                ]"
                placeholder="请输入用户名（3-20个字符）"
                @blur="validateField('username')"
                @input="clearFieldError('username')"
              />
              <transition name="fade">
                <div v-if="formData.username && !errors.username" class="absolute right-3 top-1/2 -translate-y-1/2">
                  <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                  </svg>
                </div>
              </transition>
            </div>
            <transition name="slide-down">
              <p v-if="errors.username" class="text-xs text-red-600 mt-1">
                {{ errors.username }}
              </p>
            </transition>
          </div>

          <!-- 邮箱输入框 -->
          <div class="space-y-1">
            <label for="email" class="text-sm font-medium text-gray-700 flex items-center">
              <svg class="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              邮箱地址
            </label>
            <div class="relative">
              <input
                id="email"
                v-model="formData.email"
                type="email"
                required
                :class="[
                  'w-full px-4 py-3 rounded-xl border-2 transition-all duration-200',
                  'placeholder-gray-400 text-gray-900',
                  'focus:outline-none focus:ring-4 focus:ring-indigo-100 focus:border-indigo-500',
                  'hover:border-indigo-300',
                  errors.email ? 'border-red-300 bg-red-50' : 'border-gray-200 bg-white'
                ]"
                placeholder="请输入您的邮箱地址"
                @blur="validateField('email')"
                @input="clearFieldError('email')"
              />
              <transition name="fade">
                <div v-if="formData.email && !errors.email" class="absolute right-3 top-1/2 -translate-y-1/2">
                  <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                  </svg>
                </div>
              </transition>
            </div>
            <transition name="slide-down">
              <p v-if="errors.email" class="text-xs text-red-600 mt-1">
                {{ errors.email }}
              </p>
            </transition>
          </div>

          <!-- 密码输入框 -->
          <div class="space-y-1">
            <label for="password" class="text-sm font-medium text-gray-700 flex items-center">
              <svg class="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
              密码
            </label>
            <div class="relative">
              <input
                id="password"
                v-model="formData.password"
                :type="showPassword ? 'text' : 'password'"
                required
                :class="[
                  'w-full px-4 py-3 pr-12 rounded-xl border-2 transition-all duration-200',
                  'placeholder-gray-400 text-gray-900',
                  'focus:outline-none focus:ring-4 focus:ring-indigo-100 focus:border-indigo-500',
                  'hover:border-indigo-300',
                  errors.password ? 'border-red-300 bg-red-50' : 'border-gray-200 bg-white'
                ]"
                placeholder="请设置密码（至少6个字符）"
                @blur="validateField('password')"
                @input="onPasswordInput"
              />
              <button
                type="button"
                @click="showPassword = !showPassword"
                class="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 focus:outline-none"
              >
                <svg v-if="!showPassword" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                <svg v-else class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                    d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                </svg>
              </button>
            </div>
            <!-- 密码强度指示器 -->
            <div v-if="formData.password" class="mt-2">
              <div class="flex items-center justify-between text-xs mb-1">
                <span class="text-gray-600">密码强度</span>
                <span :class="passwordStrengthColor">{{ passwordStrengthText }}</span>
              </div>
              <div class="h-2 bg-gray-200 rounded-full overflow-hidden">
                <div 
                  :class="['h-full transition-all duration-300', passwordStrengthColor.replace('text-', 'bg-')]"
                  :style="{ width: passwordStrengthWidth }"
                ></div>
              </div>
            </div>
            <transition name="slide-down">
              <p v-if="errors.password" class="text-xs text-red-600 mt-1">
                {{ errors.password }}
              </p>
            </transition>
          </div>

          <!-- 确认密码输入框 -->
          <div class="space-y-1">
            <label for="confirmPassword" class="text-sm font-medium text-gray-700 flex items-center">
              <svg class="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              确认密码
            </label>
            <div class="relative">
              <input
                id="confirmPassword"
                v-model="formData.confirmPassword"
                :type="showConfirmPassword ? 'text' : 'password'"
                required
                :class="[
                  'w-full px-4 py-3 pr-12 rounded-xl border-2 transition-all duration-200',
                  'placeholder-gray-400 text-gray-900',
                  'focus:outline-none focus:ring-4 focus:ring-indigo-100 focus:border-indigo-500',
                  'hover:border-indigo-300',
                  errors.confirmPassword ? 'border-red-300 bg-red-50' : 'border-gray-200 bg-white'
                ]"
                placeholder="请再次输入密码"
                @blur="validateField('confirmPassword')"
                @input="clearFieldError('confirmPassword')"
              />
              <button
                type="button"
                @click="showConfirmPassword = !showConfirmPassword"
                class="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 focus:outline-none"
              >
                <svg v-if="!showConfirmPassword" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                <svg v-else class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                    d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                </svg>
              </button>
            </div>
            <transition name="slide-down">
              <p v-if="errors.confirmPassword" class="text-xs text-red-600 mt-1">
                {{ errors.confirmPassword }}
              </p>
            </transition>
          </div>

          <!-- 服务条款 -->
          <div class="flex items-start">
            <input
              id="agree-terms"
              v-model="formData.agreeTerms"
              type="checkbox"
              class="h-4 w-4 mt-0.5 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded transition-colors"
            />
            <label for="agree-terms" class="ml-2 block text-sm text-gray-700">
              我已阅读并同意
              <a href="#" class="text-indigo-600 hover:text-indigo-500 transition-colors">服务条款</a>
              和
              <a href="#" class="text-indigo-600 hover:text-indigo-500 transition-colors">隐私政策</a>
            </label>
          </div>

          <!-- 注册按钮 -->
          <button
            type="submit"
            :disabled="isLoading || !isFormValid"
            :class="[
              'w-full py-3 px-4 rounded-xl font-medium text-white transition-all duration-200',
              'transform hover:scale-[1.02] active:scale-[0.98]',
              'focus:outline-none focus:ring-4 focus:ring-indigo-200',
              isLoading || !isFormValid
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-gradient-to-r from-pink-500 to-indigo-600 hover:from-pink-600 hover:to-indigo-700 shadow-lg hover:shadow-xl'
            ]"
          >
            <span v-if="!isLoading" class="flex items-center justify-center">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
              </svg>
              创建账户
            </span>
            <span v-else class="flex items-center justify-center">
              <div class="spinner-sm mr-2"></div>
              注册中...
            </span>
          </button>
        </form>

        <!-- 登录链接 -->
        <div class="text-center pt-4 border-t border-gray-200">
          <p class="text-sm text-gray-600">
            已有账户？
            <router-link to="/login" class="font-medium text-indigo-600 hover:text-indigo-500 transition-colors">
              立即登录
            </router-link>
          </p>
        </div>
      </div>

      <!-- 底部链接 -->
      <div class="mt-6 text-center">
        <p class="text-xs text-white/80">
          注册即表示您同意我们的使用条款
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import { validateEmail, validateUsername, validatePassword } from '@/utils/validation';

const router = useRouter();
const authStore = useAuthStore();

// 表单数据
const formData = ref({
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  agreeTerms: false,
});

// 界面状态
const showPassword = ref(false);
const showConfirmPassword = ref(false);
const isLoading = ref(false);
const error = ref(null);
const errors = ref({});

// 密码强度计算
const passwordStrength = computed(() => {
  const password = formData.value.password;
  if (!password) return 0;
  
  let strength = 0;
  if (password.length >= 8) strength++;
  if (password.length >= 12) strength++;
  if (/[a-z]/.test(password) && /[A-Z]/.test(password)) strength++;
  if (/\d/.test(password)) strength++;
  if (/[^a-zA-Z\d]/.test(password)) strength++;
  
  return Math.min(strength, 5);
});

const passwordStrengthWidth = computed(() => {
  return `${(passwordStrength.value / 5) * 100}%`;
});

const passwordStrengthText = computed(() => {
  const texts = ['', '很弱', '弱', '中等', '强', '很强'];
  return texts[passwordStrength.value];
});

const passwordStrengthColor = computed(() => {
  const colors = ['', 'text-red-500', 'text-orange-500', 'text-yellow-500', 'text-green-500', 'text-green-600'];
  return colors[passwordStrength.value];
});

// 表单验证
const isFormValid = computed(() => {
  return formData.value.username &&
         formData.value.email &&
         formData.value.password &&
         formData.value.confirmPassword &&
         formData.value.agreeTerms &&
         Object.keys(errors.value).length === 0;
});

// 验证单个字段
const validateField = (field) => {
  switch (field) {
    case 'username':
      if (!formData.value.username) {
        errors.value.username = '请输入用户名';
      } else {
        const usernameValidation = validateUsername(formData.value.username);
        if (!usernameValidation.valid) {
          errors.value.username = '用户名必须为3-20个字符，只能包含字母、数字和下划线';
        }
      }
      break;
    case 'email':
      if (!formData.value.email) {
        errors.value.email = '请输入邮箱地址';
      } else {
        const emailValidation = validateEmail(formData.value.email);
        if (!emailValidation.valid) {
          errors.value.email = '请输入有效的邮箱地址';
        }
      }
      break;
    case 'password':
      if (!formData.value.password) {
        errors.value.password = '请设置密码';
      } else {
        const passwordValidation = validatePassword(formData.value.password);
        if (!passwordValidation.valid) {
          errors.value.password = passwordValidation.error || '密码格式不正确';
        }
      }
      // 密码改变时也验证确认密码
      if (formData.value.confirmPassword) {
        validateField('confirmPassword');
      }
      break;
    case 'confirmPassword':
      if (!formData.value.confirmPassword) {
        errors.value.confirmPassword = '请确认密码';
      } else if (formData.value.confirmPassword !== formData.value.password) {
        errors.value.confirmPassword = '两次输入的密码不一致';
      }
      break;
  }
};

// 清除字段错误
const clearFieldError = (field) => {
  delete errors.value[field];
};

// 处理密码输入
const onPasswordInput = () => {
  clearFieldError('password');
  // 如果密码现在匹配，也清除确认密码错误
  if (formData.value.confirmPassword === formData.value.password) {
    clearFieldError('confirmPassword');
  }
};

// 处理表单提交
const handleSubmit = async () => {
  // 验证所有字段
  validateField('username');
  validateField('email');
  validateField('password');
  validateField('confirmPassword');
  
  if (!isFormValid.value) {
    if (!formData.value.agreeTerms) {
      error.value = '请同意服务条款和隐私政策';
    }
    return;
  }
  
  isLoading.value = true;
  error.value = null;
  
  try {
    const result = await authStore.register({
      username: formData.value.username,
      email: formData.value.email,
      password: formData.value.password,
    });
    
    if (!result.success) {
      error.value = result.message || '注册失败，请稍后重试';
    } else {
      // 注册成功，重定向到登录页面
      router.push('/login');
    }
  } catch (err) {
    error.value = '发生意外错误，请稍后重试';
    console.error('注册错误:', err);
  } finally {
    isLoading.value = false;
  }
};

// 挂载时自动聚焦用户名字段
onMounted(() => {
  document.getElementById('username')?.focus();
});
</script>

<style scoped>
/* 如需要可添加额外的自定义样式 */
</style>