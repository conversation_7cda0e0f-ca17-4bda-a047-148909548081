# API Configuration
VITE_API_BASE_URL=http://localhost:8080/api
VITE_API_TIMEOUT=30000

# OAuth Configuration
VITE_OAUTH_GOOGLE_CLIENT_ID=your_google_client_id
VITE_OAUTH_FACEBOOK_APP_ID=your_facebook_app_id
VITE_OAUTH_GITHUB_CLIENT_ID=your_github_client_id
VITE_OAUTH_MICROSOFT_CLIENT_ID=your_microsoft_client_id

# App Configuration
VITE_APP_NAME=Mock Service
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=Professional Mock Service Platform

# Security
VITE_ENABLE_MFA=true
VITE_SESSION_TIMEOUT=3600000
VITE_REMEMBER_ME_DURATION=604800000

# Feature Flags
VITE_ENABLE_SOCIAL_LOGIN=true
VITE_ENABLE_EMAIL_VERIFICATION=true
VITE_ENABLE_PASSWORD_RECOVERY=true