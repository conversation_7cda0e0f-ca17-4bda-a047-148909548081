{"name": "mock-client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@tailwindcss/forms": "^0.5.10", "@vuelidate/core": "^2.0.3", "@vuelidate/validators": "^2.0.4", "@vueuse/core": "^13.6.0", "axios": "^1.11.0", "pinia": "^3.0.3", "vee-validate": "4", "vue": "^3.5.18", "vue-i18n": "9", "vue-router": "4", "vue-toastification": "2.0.0-rc.5", "yup": "^1.7.0"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.1", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.1", "vite": "^7.1.0"}}