# Spring Boot Mock Service

一个基于 Spring Boot 的 Mock 服务系统，提供灵活的 API 模拟功能，支持动态配置和管理。

## 📋 项目描述

本项目是一个完整的 Mock 服务解决方案，包含后端服务和前端管理界面，帮助开发团队快速创建和管理 API 模拟数据，提高开发和测试效率。

## 🏗️ 项目结构

```
springboot-mock-service/
├── mock-service/          # 后端服务 (Spring Boot)
│   ├── src/main/java/
│   │   └── cn/bughub/
│   │       ├── common/    # 通用工具类和异常处理
│   │       ├── config/    # 配置类
│   │       ├── controller/# 控制器
│   │       ├── entity/    # 实体类
│   │       └── mapper/    # 数据访问层
│   └── pom.xml
├── mock-client/           # 前端界面 (Vue 3)
│   ├── src/
│   │   ├── components/    # 组件
│   │   ├── views/         # 页面
│   │   ├── services/      # API 服务
│   │   └── stores/        # 状态管理
│   └── package.json
└── doc/                   # 项目文档
    ├── API接口文档.md
    ├── Mock服务系统需求文档.md
    ├── 数据库设计文档.md
    └── 系统架构设计说明.md
```

## 🚀 技术栈

### 后端
- **Java 17+**
- **Spring Boot 3.x**
- **MyBatis Plus** - 数据访问层
- **MySQL** - 数据库
- **Maven** - 依赖管理

### 前端
- **Vue 3** - 前端框架
- **Vite** - 构建工具
- **Tailwind CSS** - 样式框架
- **Pinia** - 状态管理
- **Vue Router** - 路由管理

## 📦 安装和运行

### 环境要求
- Java 17 或更高版本
- Node.js 18 或更高版本
- MySQL 8.0 或更高版本
- Maven 3.6 或更高版本

### 后端服务启动

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd springboot-mock-service
   ```

2. **配置数据库**
   - 创建数据库：`mock_service`
   - 执行 SQL 脚本：`doc/mock_service_ddl.sql`
   - 修改配置文件：`mock-service/src/main/resources/application.yml`

3. **启动后端服务**
   ```bash
   cd mock-service
   mvn clean install
   mvn spring-boot:run
   ```

   服务将在 `http://localhost:8080` 启动

### 前端应用启动

1. **安装依赖**
   ```bash
   cd mock-client
   npm install
   # 或使用 pnpm
   pnpm install
   ```

2. **启动开发服务器**
   ```bash
   npm run dev
   # 或使用 pnpm
   pnpm dev
   ```

   应用将在 `http://localhost:5173` 启动

## 🔧 配置说明

### 后端配置
主要配置文件位于 `mock-service/src/main/resources/application.yml`：

```yaml
server:
  port: 8080

spring:
  datasource:
    url: ****************************************
    username: your_username
    password: your_password
    driver-class-name: com.mysql.cj.jdbc.Driver
```

### 前端配置
前端配置文件位于 `mock-client/src/config/index.js`：

```javascript
export default {
  API_BASE_URL: 'http://localhost:8080',
  // 其他配置...
}
```

## 📚 功能特性

- ✅ 用户认证和授权
- ✅ Mock API 创建和管理
- ✅ 动态响应配置
- ✅ 请求日志记录
- ✅ 响应式前端界面
- ✅ OAuth 第三方登录支持
- ✅ 分页和搜索功能

## 📖 API 文档

详细的 API 文档请参考：[API接口文档.md](doc/API接口文档.md)

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 👥 作者

- **开发团队** - *初始工作* - [GitHub](https://github.com/zhengwenj)

## 🔗 相关链接

- [项目文档](doc/)
- [问题反馈](https://github.com/zhengwenj/springboot-mock-service/issues)
- [更新日志](CHANGELOG.md)

## ⭐ 致谢

感谢所有为这个项目做出贡献的开发者！

---

**注意：** 请确保在生产环境中修改默认的安全配置和数据库凭据。